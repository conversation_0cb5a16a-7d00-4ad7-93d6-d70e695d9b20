const mongoose = require('mongoose');

const BotSettingsSchema = new mongoose.Schema(
  {
    welcomeMessage: {
      type: String,
      default: '🎮 *Spor Tahmin Yarışması Botuna Hoş Geldiniz!* 🎮\n\nBu bot ile çeşitli spor etkinlikleri hakkında tahminlerde bulunabilir, yarışmalara katılabilir ve ödüller kazanabilirsiniz.\n\nBaşlamak için lütfen bir kullanıcı adı belirleyin.',
    },
    channelRequirement: {
      required: {
        type: Boolean,
        default: false,
      },
      channelUsername: {
        type: String,
        default: '',
      },
    },
    botUsername: {
      type: String,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    maintenanceMode: {
      type: Boolean,
      default: false,
    },
    maintenanceMessage: {
      type: String,
      default: 'Bot is currently under maintenance. Please try again later.',
    },
    notifications: {
      dailyLimit: {
        type: Number,
        default: 2,
        min: 0,
        max: 10000
      },
      batchSize: {
        type: Number,
        default: 100,
        min: 10,
        max: 1000
      },
      delayBetweenBatches: {
        type: Number,
        default: 2000, // milisaniye cinsinden
        min: 500,
        max: 10000
      },
      maxRecipientsPerNotification: {
        type: Number,
        default: 10000,
        min: 100,
        max: 100000
      }
    },
    shortUrlDomain: {
      type: String,
      default: 'https://t.me'
    }
  },
  {
    timestamps: true,
  }
);

// Ensure only one settings document exists
BotSettingsSchema.statics.getSettings = async function () {
  const settings = await this.findOne();

  // Check if settings exist
  if (settings) {
    // Güncellenecek ayarları kontrol et
    let needsUpdate = false;

    // Günlük limit kontrolü
    if (process.env.NOTIFICATION_DAILY_LIMIT) {
      const envDailyLimit = parseInt(process.env.NOTIFICATION_DAILY_LIMIT, 10);
      if (!isNaN(envDailyLimit) && envDailyLimit !== settings.notifications.dailyLimit) {
        console.log(`Updating notification daily limit from .env: ${envDailyLimit}`);
        settings.notifications.dailyLimit = envDailyLimit;
        needsUpdate = true;
      }
    }

    // Batch size kontrolü
    if (process.env.NOTIFICATION_BATCH_SIZE) {
      const envBatchSize = parseInt(process.env.NOTIFICATION_BATCH_SIZE, 10);
      if (!isNaN(envBatchSize) && envBatchSize !== settings.notifications.batchSize) {
        console.log(`Updating notification batch size from .env: ${envBatchSize}`);
        settings.notifications.batchSize = envBatchSize;
        needsUpdate = true;
      }
    }

    // Batch arası gecikme kontrolü
    if (process.env.NOTIFICATION_DELAY_BETWEEN_BATCHES) {
      const envDelay = parseInt(process.env.NOTIFICATION_DELAY_BETWEEN_BATCHES, 10);
      if (!isNaN(envDelay) && envDelay !== settings.notifications.delayBetweenBatches) {
        console.log(`Updating notification delay between batches from .env: ${envDelay}`);
        settings.notifications.delayBetweenBatches = envDelay;
        needsUpdate = true;
      }
    }

    // Maksimum alıcı sayısı kontrolü
    if (process.env.NOTIFICATION_MAX_RECIPIENTS) {
      const envMaxRecipients = parseInt(process.env.NOTIFICATION_MAX_RECIPIENTS, 10);
      if (!isNaN(envMaxRecipients) && envMaxRecipients !== settings.notifications.maxRecipientsPerNotification) {
        console.log(`Updating notification max recipients from .env: ${envMaxRecipients}`);
        settings.notifications.maxRecipientsPerNotification = envMaxRecipients;
        needsUpdate = true;
      }
    }

    // Değişiklik varsa kaydet
    if (needsUpdate) {
      await settings.save();
      console.log('Bot settings updated from .env file');
    }

    return settings;
  }

  // Create default settings if none exist
  const dailyLimit = process.env.NOTIFICATION_DAILY_LIMIT ?
    parseInt(process.env.NOTIFICATION_DAILY_LIMIT, 10) : 2;
  const batchSize = process.env.NOTIFICATION_BATCH_SIZE ?
    parseInt(process.env.NOTIFICATION_BATCH_SIZE, 10) : 100;
  const delayBetweenBatches = process.env.NOTIFICATION_DELAY_BETWEEN_BATCHES ?
    parseInt(process.env.NOTIFICATION_DELAY_BETWEEN_BATCHES, 10) : 2000;
  const maxRecipientsPerNotification = process.env.NOTIFICATION_MAX_RECIPIENTS ?
    parseInt(process.env.NOTIFICATION_MAX_RECIPIENTS, 10) : 10000;

  return await this.create({
    botUsername: 'YourBotUsername',
    notifications: {
      dailyLimit: isNaN(dailyLimit) ? 2 : dailyLimit,
      batchSize: isNaN(batchSize) ? 100 : batchSize,
      delayBetweenBatches: isNaN(delayBetweenBatches) ? 2000 : delayBetweenBatches,
      maxRecipientsPerNotification: isNaN(maxRecipientsPerNotification) ? 10000 : maxRecipientsPerNotification
    }
  });
};

module.exports = mongoose.model('BotSettings', BotSettingsSchema);
