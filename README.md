# Telegram Sports Prediction Contest Bot

A Telegram bot for sports prediction contests with a web admin panel. Built with Node.js, Express, MongoDB, and the Telegram Bot API.

## Features

- Telegram bot for users to participate in prediction contests
- Web admin panel for managing contests, users, and bot settings
- Create contests with custom questions and answer options
- Set channel subscription requirements for participation
- Calculate winners based on correct answers
- Export contest results to Excel

## Prerequisites

- Node.js (v14 or higher)
- MongoDB
- Telegram <PERSON> (from BotFather)

## Installation

1. Clone the repository:
```
git clone <repository-url>
cd telegram-bot-loto
```

2. Install dependencies:
```
npm install
```

3. Create a `.env` file in the root directory with the following variables:
```
PORT=3000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/telegram-loto
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
```

4. Initialize the database with default admin user:
```
npm run init-db
```

## Running the Application

### Development Mode
```
npm run dev
```

### Production Mode
```
npm start
```

## Default Admin Credentials

- Username: admin
- Password: admin123

**Important:** Change the default admin password after first login.

## Bot Commands

- `/start` - Start the bot and register
- `/contests` - View active contests
- `/profile` - View your profile and contest history
- `/help` - Show help message

## Admin Panel

The admin panel is accessible at `http://localhost:3000` (or your configured port).

### Admin Panel Features

- Dashboard with statistics
- Contest management (create, edit, delete)
- User management
- Bot settings configuration
- Contest results and exports

## Project Structure

```
telegram-bot-loto/
├── src/
│   ├── bot/              # Telegram bot implementation
│   ├── config/           # Configuration files
│   ├── controllers/      # API controllers
│   ├── middleware/       # Express middleware
│   ├── models/           # MongoDB models
│   ├── routes/           # API routes
│   ├── utils/            # Utility functions
│   └── index.js          # Main application entry point
├── public/               # Web admin panel frontend
├── .env                  # Environment variables
├── package.json          # Project dependencies
└── README.md             # Project documentation
```

## License

ISC
