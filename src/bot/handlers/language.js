const { getTranslation } = require('../translations');

const handleLanguage = async (bot, msg, user, selectedLanguage = null) => {
  try {
    const chatId = msg.chat.id;

    // If no language is selected, show language selection
    if (!selectedLanguage) {
      const languageKeyboard = {
        inline_keyboard: [
          [
            { text: 'Türkçe 🇹🇷', callback_data: 'language_tr' },
            { text: 'English 🇬🇧', callback_data: 'language_en' },
          ],
          [
            { text: 'Deutsch 🇩🇪', callback_data: 'language_de' },
            { text: 'العربية 🇸🇦', callback_data: 'language_ar' },
          ],
        ],
      };

      // Get language name
      const languageNames = {
        tr: 'Türkçe',
        en: 'English',
        de: 'Deutsch',
        ar: 'العربية',
      };

      const currentLanguage = languageNames[user.language] || 'Türkçe';

      // Get message based on user language
      const currentLangMessage = await getTranslation(user.language, 'bot.language.current', { language: currentLanguage });
      const selectMessage = await getTranslation(user.language, 'bot.language.select');
      const message = `${currentLangMessage}\n\n${selectMessage}`;

      bot.sendMessage(
        chatId,
        message,
        { reply_markup: languageKeyboard }
      );
    } else {
      // Update user language
      user.language = selectedLanguage;
      await user.save();

      // Get language name
      const languageNames = {
        tr: 'Türkçe',
        en: 'English',
        de: 'Deutsch',
        ar: 'العربية',
      };

      const languageName = languageNames[selectedLanguage] || 'Türkçe';

      // Get confirmation message based on selected language
      const message = await getTranslation(selectedLanguage, 'bot.language.changed', { language: languageName });

      // Send confirmation message
      bot.sendMessage(
        chatId,
        message
      );
    }
  } catch (error) {
    console.error('Error handling language command:', error);
    const errorMessage = await getTranslation(user?.language || 'tr', 'bot.errors.general');
    bot.sendMessage(
      chatId,
      errorMessage
    );
  }
};

module.exports = { handleLanguage };
