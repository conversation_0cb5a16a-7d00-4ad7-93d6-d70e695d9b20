const Contest = require('../../models/Contest');
const Submission = require('../../models/Submission');
const { getTranslation } = require('../translations');
const { EMOJI_GAMES, SUCCESS_MESSAGES } = require('./emojiGames');

// Handle emoji game results
const handleEmojiGameResult = async (bot, msg, user) => {
  const chatId = msg.chat.id;

  try {
    // Check if user is playing emoji game
    if (user.state !== 'PLAYING_EMOJI_GAME') {
      return; // Ignore if not playing
    }

    // Get contest and submission
    const contest = await Contest.findById(user.currentContest);
    const submission = await Submission.findById(user.currentSubmission);

    if (!contest || !submission) {
      // Reset user state
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentSubmission = null;
      await user.save();
      return;
    }

    const gameType = contest.emojiGame.gameType;
    let result = null;

    // Extract result based on game type
    switch (gameType) {
      case 'DICE':
        if (msg.dice) {
          result = msg.dice.value;
        }
        break;
      case 'BASKETBALL':
        if (msg.dice && msg.dice.emoji === '🏀') {
          result = msg.dice.value; // 1-4 miss, 5 success
        }
        break;
      case 'FOOTBALL':
        if (msg.dice && msg.dice.emoji === '⚽') {
          result = msg.dice.value; // 1-4 miss, 5 success
        }
        break;
      case 'DART':
        if (msg.dice && msg.dice.emoji === '🎯') {
          result = msg.dice.value; // 1-5 miss, 6 bullseye
        }
        break;
      case 'BOWLING':
        if (msg.dice && msg.dice.emoji === '🎳') {
          result = msg.dice.value; // 1-5 miss, 6 strike
        }
        break;
      case 'SLOT':
        if (msg.dice && msg.dice.emoji === '🎰') {
          // Slot machine returns array of 3 values
          result = [msg.dice.value]; // This might need adjustment based on Telegram API
        }
        break;
    }

    if (result === null) {
      return; // Not a valid emoji for this game
    }

    // Add attempt to submission
    const attemptNumber = submission.emojiResults.attempts.length + 1;
    submission.emojiResults.attempts.push({
      attemptNumber,
      result,
      timestamp: new Date(),
    });

    // Send reaction message
    await sendReactionMessage(bot, chatId, user, gameType, result, attemptNumber);

    // Check if all attempts are completed
    if (attemptNumber >= contest.emojiGame.attemptCount) {
      // Calculate final result
      const finalResult = calculateFinalResult(contest, submission);
      submission.emojiResults.totalScore = finalResult.score;
      submission.emojiResults.achievedTarget = finalResult.achieved;
      submission.isWinner = finalResult.achieved;

      await submission.save();

      // Reset user state
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentSubmission = null;
      await user.save();

      // Send final result
      await sendFinalResult(bot, chatId, user, contest, submission, finalResult);
    } else {
      // Save current progress
      await submission.save();

      // Send next attempt prompt
      const nextAttempt = attemptNumber + 1;
      const emoji = EMOJI_GAMES[gameType].emoji;
      const nextMessage = await getTranslation(user.language, 'bot.emojiGames.nextAttempt', {
        emoji: emoji,
        attempt: nextAttempt,
        total: contest.emojiGame.attemptCount,
      });

      await bot.sendMessage(chatId, nextMessage);
    }

  } catch (error) {
    console.error('Error handling emoji game result:', error);

    // Reset user state on error
    user.state = 'IDLE';
    user.currentContest = null;
    user.currentSubmission = null;
    await user.save();

    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Send reaction message based on result
const sendReactionMessage = async (bot, chatId, user, gameType, result, attemptNumber) => {
  let isSuccess = false;
  let message = '';

  switch (gameType) {
    case 'DICE':
      message = await getTranslation(user.language, 'bot.emojiGames.diceResult', {
        attempt: attemptNumber,
        result: result,
      });
      if (result === 6) {
        isSuccess = true;
      }
      break;
    case 'BASKETBALL':
      if (result === 5) {
        isSuccess = true;
        const successMsg = SUCCESS_MESSAGES.BASKETBALL[user.language] || SUCCESS_MESSAGES.BASKETBALL.tr;
        message = successMsg[Math.floor(Math.random() * successMsg.length)];
      } else {
        message = await getTranslation(user.language, 'bot.emojiGames.basketballMiss', {
          attempt: attemptNumber,
        });
      }
      break;
    case 'FOOTBALL':
      if (result === 3 || result === 4 || result === 5) {
        isSuccess = true;
        const successMsg = SUCCESS_MESSAGES.FOOTBALL[user.language] || SUCCESS_MESSAGES.FOOTBALL.tr;
        message = successMsg[Math.floor(Math.random() * successMsg.length)];
      } else {
        message = await getTranslation(user.language, 'bot.emojiGames.footballMiss', {
          attempt: attemptNumber,
        });
      }
      break;
    case 'DART':
      if (result === 6) {
        isSuccess = true;
        const successMsg = SUCCESS_MESSAGES.DART[user.language] || SUCCESS_MESSAGES.DART.tr;
        message = successMsg[Math.floor(Math.random() * successMsg.length)];
      } else {
        message = await getTranslation(user.language, 'bot.emojiGames.dartMiss', {
          attempt: attemptNumber,
          result: result,
        });
      }
      break;
    case 'BOWLING':
      if (result === 6) {
        isSuccess = true;
        const successMsg = SUCCESS_MESSAGES.BOWLING[user.language] || SUCCESS_MESSAGES.BOWLING.tr;
        message = successMsg[Math.floor(Math.random() * successMsg.length)];
      } else {
        message = await getTranslation(user.language, 'bot.emojiGames.bowlingMiss', {
          attempt: attemptNumber,
        });
      }
      break;
    case 'SLOT':
      // For slot machine, we need to check winning combinations
      // This is simplified - actual implementation would need proper slot result parsing
      message = await getTranslation(user.language, 'bot.emojiGames.slotResult', {
        attempt: attemptNumber,
      });
      break;
  }

  await bot.sendMessage(chatId, message);
};

// Calculate final result
const calculateFinalResult = (contest, submission) => {
  const gameType = contest.emojiGame.gameType;
  const attempts = submission.emojiResults.attempts;
  let score = 0;
  let achieved = false;

  switch (gameType) {
    case 'DICE':
      if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
        score = attempts.reduce((sum, attempt) => sum + attempt.result, 0);
        achieved = score >= contest.emojiGame.diceSettings.targetValue;
      } else if (contest.emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT') {
        score = attempts.filter(attempt => attempt.result === contest.emojiGame.diceSettings.targetValue).length;
        achieved = score >= contest.emojiGame.diceSettings.targetCount;
      }
      break;
    case 'BASKETBALL':
      // Count successful attempts (result 4 or 5)
      score = attempts.filter(attempt => attempt.result === 4 || attempt.result === 5).length;
      achieved = score >= contest.emojiGame.successTarget;
      break;
    case 'FOOTBALL':
      // Count successful attempts (result 3, 4 or 5)
      score = attempts.filter(attempt => attempt.result === 3 || attempt.result === 4 || attempt.result === 5).length;
      achieved = score >= contest.emojiGame.successTarget;
      break;
    case 'DART':
      // Count bullseye attempts (result 6)
      score = attempts.filter(attempt => attempt.result === 6).length;
      achieved = score >= contest.emojiGame.bullseyeTarget;
      break;
    case 'BOWLING':
      // Count strike attempts (result 6)
      score = attempts.filter(attempt => attempt.result === 6).length;
      achieved = score >= contest.emojiGame.strikeTarget;
      break;
    case 'SLOT':
      // Count winning combinations
      score = attempts.filter(attempt => attempt.success).length;
      achieved = score > 0; // Any winning combination
      break;
  }

  return { score, achieved };
};

// Send final result
const sendFinalResult = async (bot, chatId, user, contest, submission, finalResult) => {
  let message = '';

  if (finalResult.achieved) {
    // Winner message with fireworks
    message = await getTranslation(user.language, 'bot.emojiGames.winner', {
      title: contest.title,
      score: finalResult.score,
    });
    message = `🎆🎇✨ ${message} ✨🎇🎆`;
  } else {
    // Not winner message
    message = await getTranslation(user.language, 'bot.emojiGames.notWinner', {
      title: contest.title,
      score: finalResult.score,
    });
  }

  await bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });

  // Send participation summary
  const summaryMessage = await getTranslation(user.language, 'bot.emojiGames.summary', {
    attempts: submission.emojiResults.attempts.length,
    score: finalResult.score,
  });

  await bot.sendMessage(chatId, summaryMessage);
};

module.exports = {
  handleEmojiGameResult,
};
