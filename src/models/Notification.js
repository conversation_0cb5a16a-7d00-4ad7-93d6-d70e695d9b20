const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    image: {
      type: String, // Dosya yolu
      default: null
    },
    urls: [{
      originalUrl: String,
      shortCode: String,
      clicks: {
        type: Number,
        default: 0
      },
      clickedBy: [{
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        clickedAt: {
          type: Date,
          default: Date.now
        }
      }]
    }],
    buttons: [{
      text: String,
      url: String,
      clicks: {
        type: Number,
        default: 0
      },
      clickedBy: [{
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        clickedAt: {
          type: Date,
          default: Date.now
        }
      }]
    }],
    status: {
      type: String,
      enum: ['DRAFT', 'SCHEDULED', 'SENDING', 'PAUSED', 'COMPLETED', 'FAILED'],
      default: 'DRAFT'
    },
    scheduledFor: {
      type: Date
    },
    startedAt: {
      type: Date
    },
    completedAt: {
      type: Date
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      required: true
    },
    totalRecipients: {
      type: Number,
      default: 0
    },
    successCount: {
      type: Number,
      default: 0
    },
    failedCount: {
      type: Number,
      default: 0
    },
    pendingCount: {
      type: Number,
      default: 0
    },
    currentIndex: {
      type: Number,
      default: 0
    },
    recipients: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      status: {
        type: String,
        enum: ['PENDING', 'SENT', 'FAILED', 'RETRY'],
        default: 'PENDING'
      },
      sentAt: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      retryCount: {
        type: Number,
        default: 0
      },
      lastRetryAt: {
        type: Date
      }
    }],
    lastProcessedAt: {
      type: Date
    },
    retryCount: {
      type: Number,
      default: 0
    },
    lastRetryAt: {
      type: Date
    }
  },
  {
    timestamps: true
  }
);

// Metot: Bildirim durumunu güncelleme
NotificationSchema.methods.updateStatus = async function (status) {
  this.status = status;

  if (status === 'SENDING') {
    this.startedAt = this.startedAt || new Date();
  } else if (status === 'COMPLETED') {
    this.completedAt = new Date();
  }

  await this.save();
  return this;
};

// Not: updateStats fonksiyonu kaldırıldı, istatistikler artık updateRecipientStatus içinde güncelleniyor

// Metot: URL tıklamasını kaydetme
NotificationSchema.methods.recordUrlClick = async function (urlIndex, userId) {
  if (!this.urls[urlIndex]) return null;

  this.urls[urlIndex].clicks++;

  const userClick = this.urls[urlIndex].clickedBy.find(
    click => click.user.toString() === userId.toString()
  );

  if (userClick) {
    userClick.clickedAt = new Date();
  } else {
    this.urls[urlIndex].clickedBy.push({
      user: userId,
      clickedAt: new Date()
    });
  }

  await this.save();
  return this;
};

// Metot: Buton tıklamasını kaydetme
NotificationSchema.methods.recordButtonClick = async function (buttonIndex, userId) {
  if (!this.buttons || !this.buttons[buttonIndex]) return null;

  this.buttons[buttonIndex].clicks++;

  const userClick = this.buttons[buttonIndex].clickedBy.find(
    click => click.user.toString() === userId.toString()
  );

  if (userClick) {
    userClick.clickedAt = new Date();
  } else {
    this.buttons[buttonIndex].clickedBy.push({
      user: userId,
      clickedAt: new Date()
    });
  }

  await this.save();
  return this;
};

// Metot: Alıcı durumunu güncelleme
NotificationSchema.methods.updateRecipientStatus = async function (userId, status, errorMessage = null, incrementRetry = false) {
  // userId ve recipient.user'ı string'e çevirme işlemi
  const userIdStr = String(userId);

  // Tüm alıcıları ve ID'lerini logla
  console.log(`[updateRecipientStatus] Aranan kullanıcı ID: ${userIdStr}`);
  console.log(`[updateRecipientStatus] Toplam alıcı sayısı: ${this.recipients.length}`);

  // Alıcıyı bul
  let recipientIndex = -1;

  // Önce doğrudan ID karşılaştırması dene
  for (let i = 0; i < this.recipients.length; i++) {
    const r = this.recipients[i];
    if (!r.user) continue;

    // MongoDB ObjectId'yi string'e çevir
    let recipientUserId = '';

    if (typeof r.user === 'object' && r.user.$oid) {
      // MongoDB JSON formatı ($oid alanı varsa)
      recipientUserId = r.user.$oid;
      console.log(`Alıcı #${i} $oid formatında: ${recipientUserId}`);
    } else {
      // Doğrudan ObjectId veya string
      recipientUserId = String(r.user);
      console.log(`Alıcı #${i} normal string formatında: ${recipientUserId}`);
    }

    if (recipientUserId === userIdStr) {
      console.log(`Tam eşleşme bulundu: ${recipientUserId} = ${userIdStr}`);
      recipientIndex = i;
      break;
    }
  }

  // Eğer bulunamadıysa, son 24 karakteri karşılaştır (MongoDB ObjectId 24 karakterdir)
  if (recipientIndex === -1 && userIdStr.length >= 24) {
    const userIdLast24 = userIdStr.slice(-24);
    console.log(`24 karakter ID ile aranıyor: ${userIdLast24}`);

    for (let i = 0; i < this.recipients.length; i++) {
      const r = this.recipients[i];
      if (!r.user) continue;

      let recipientUserId = '';

      if (typeof r.user === 'object' && r.user.$oid) {
        recipientUserId = r.user.$oid;
      } else {
        recipientUserId = String(r.user);
      }

      const recipientIdLast24 = recipientUserId.slice(-24);

      if (recipientIdLast24 === userIdLast24) {
        console.log(`24 karakter eşleşme bulundu: ${recipientIdLast24}`);
        recipientIndex = i;
        break;
      }
    }
  }

  if (recipientIndex === -1) {
    console.log(`[updateRecipientStatus] Kullanıcı bulunamadı: ${userIdStr}`);

    // Hata ayıklama için ilk 5 alıcıyı logla
    const firstFiveRecipients = this.recipients.slice(0, 5).map(r => ({
      user: r.user ? String(r.user) : 'undefined',
      status: r.status
    }));
    console.log(`[updateRecipientStatus] İlk 5 alıcı:`, JSON.stringify(firstFiveRecipients));

    return null;
  }

  const oldStatus = this.recipients[recipientIndex].status;
  this.recipients[recipientIndex].status = status;

  console.log(`[updateRecipientStatus] Kullanıcı: ${userId}, Durum değişikliği: ${oldStatus} -> ${status}`);

  if (status === 'SENT') {
    this.recipients[recipientIndex].sentAt = new Date();
    // Başarılı gönderimde retry sayacını sıfırla
    this.recipients[recipientIndex].retryCount = 0;
  } else if (status === 'FAILED') {
    this.recipients[recipientIndex].errorMessage = errorMessage;

    // Yeniden deneme sayısını artır (eğer isteniyorsa)
    if (incrementRetry) {
      this.recipients[recipientIndex].retryCount += 1;
      this.recipients[recipientIndex].lastRetryAt = new Date();
    }
  } else if (status === 'RETRY') {
    // Yeniden deneme durumu
    this.recipients[recipientIndex].lastRetryAt = new Date();
    if (errorMessage) {
      this.recipients[recipientIndex].errorMessage = errorMessage;
    }
  }

  this.lastProcessedAt = new Date();

  // İstatistikleri manuel olarak hesapla
  let sentCount = 0;
  let pendingCount = 0;
  let failedCount = 0;
  let retryCount = 0;

  this.recipients.forEach(recipient => {
    if (recipient.status === 'SENT') {
      sentCount++;
    } else if (recipient.status === 'FAILED') {
      failedCount++;
    } else if (recipient.status === 'RETRY') {
      retryCount++;
    } else if (recipient.status === 'PENDING') {
      pendingCount++;
    }
  });

  // İstatistikleri güncelle
  this.successCount = sentCount;
  this.failedCount = failedCount;
  this.pendingCount = pendingCount + retryCount;

  // Değişiklikleri kaydet
  await this.save();

  console.log(`[updateRecipientStatus] İstatistikler güncellendi. Toplam: ${this.recipients.length}, Başarılı: ${sentCount}, Başarısız: ${failedCount}, Bekleyen: ${pendingCount + retryCount}`);

  return this;
};

module.exports = mongoose.model('Notification', NotificationSchema);
