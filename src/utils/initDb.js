const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Admin = require('../models/Admin');
const BotSettings = require('../models/BotSettings');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Create default admin user
const createDefaultAdmin = async () => {
  try {
    // Check if admin exists
    const adminExists = await Admin.findOne({ username: 'admin' });
    
    if (adminExists) {
      console.log('Default admin already exists');
      return;
    }
    
    // Create admin
    const admin = await Admin.create({
      username: 'admin',
      password: 'admin123',
      email: '<EMAIL>',
      name: 'Administrator',
      role: 'superadmin',
    });
    
    console.log('Default admin created:', admin.username);
  } catch (error) {
    console.error('Error creating default admin:', error);
  }
};

// Create default bot settings
const createDefaultBotSettings = async () => {
  try {
    // Check if settings exist
    const settings = await BotSettings.findOne();
    
    if (settings) {
      console.log('Bot settings already exist');
      return;
    }
    
    // Create settings
    const botSettings = await BotSettings.create({
      botUsername: 'YourBotUsername',
      welcomeMessage: 'Welcome to the Sports Prediction Contest Bot!',
      channelRequirement: {
        required: false,
        channelUsername: '',
      },
      isActive: true,
      maintenanceMode: false,
      maintenanceMessage: 'Bot is currently under maintenance. Please try again later.',
    });
    
    console.log('Default bot settings created');
  } catch (error) {
    console.error('Error creating default bot settings:', error);
  }
};

// Run initialization
const init = async () => {
  try {
    await createDefaultAdmin();
    await createDefaultBotSettings();
    
    console.log('Database initialization completed');
    process.exit(0);
  } catch (error) {
    console.error('Initialization error:', error);
    process.exit(1);
  }
};

// Run the initialization
init();
