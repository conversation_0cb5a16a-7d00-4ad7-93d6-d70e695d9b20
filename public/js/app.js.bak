// API base URL
const API_BASE_URL = '/api';

// App state
const state = {
  isAuthenticated: false,
  admin: null,
  token: null,
  currentPage: 'login',
};

// DOM elements
const app = document.getElementById('app');
const navItems = document.getElementById('nav-items');
const authItems = document.getElementById('auth-items');

// Initialize the app
function init() {
  // Check if token exists in localStorage
  const token = localStorage.getItem('token');
  if (token) {
    state.token = token;
    state.isAuthenticated = true;
    state.admin = JSON.parse(localStorage.getItem('admin'));

    // Set axios default header
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Load dashboard
    loadDashboard();
  } else {
    // Load login page
    loadLoginPage();
  }

  // Update navigation
  updateNavigation();
}

// Update navigation based on authentication state
function updateNavigation() {
  // Clear navigation
  navItems.innerHTML = '';
  authItems.innerHTML = '';

  if (state.isAuthenticated) {
    // Add navigation items for authenticated users
    navItems.innerHTML = `
      <li class="nav-item">
        <a class="nav-link ${state.currentPage === 'dashboard' ? 'active' : ''}" href="#" onclick="loadDashboard()">Dashboard</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${state.currentPage === 'contests' ? 'active' : ''}" href="#" onclick="loadContests()">Contests</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${state.currentPage === 'users' ? 'active' : ''}" href="#" onclick="loadUsers()">Users</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${state.currentPage === 'settings' ? 'active' : ''}" href="#" onclick="loadSettings()">Bot Settings</a>
      </li>
    `;

    // Add auth items
    authItems.innerHTML = `
      <li class="nav-item">
        <span class="nav-link">Welcome, ${state.admin.name}</span>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" onclick="logout()">Logout</a>
      </li>
    `;
  } else {
    // Add auth items for non-authenticated users
    authItems.innerHTML = `
      <li class="nav-item">
        <a class="nav-link ${state.currentPage === 'login' ? 'active' : ''}" href="#" onclick="loadLoginPage()">Login</a>
      </li>
    `;
  }
}

// Load login page
function loadLoginPage() {
  state.currentPage = 'login';
  updateNavigation();

  app.innerHTML = `
    <div class="login-container">
      <h2 class="text-center mb-4">Admin Login</h2>
      <form id="login-form">
        <div class="mb-3">
          <label for="username" class="form-label">Username</label>
          <input type="text" class="form-control" id="username" required>
        </div>
        <div class="mb-3">
          <label for="password" class="form-label">Password</label>
          <input type="password" class="form-control" id="password" required>
        </div>
        <div id="login-error" class="alert alert-danger d-none"></div>
        <button type="submit" class="btn btn-primary w-100">Login</button>
      </form>
    </div>
  `;

  // Add event listener to login form
  document.getElementById('login-form').addEventListener('submit', handleLogin);
}

// Handle login form submission
async function handleLogin(e) {
  e.preventDefault();

  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const errorElement = document.getElementById('login-error');

  try {
    errorElement.classList.add('d-none');

    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username,
      password,
    });

    // Save token and admin info
    const { token, admin } = response.data;
    localStorage.setItem('token', token);
    localStorage.setItem('admin', JSON.stringify(admin));

    // Update state
    state.token = token;
    state.admin = admin;
    state.isAuthenticated = true;

    // Set axios default header
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Load dashboard
    loadDashboard();

    // Update navigation
    updateNavigation();
  } catch (error) {
    console.error('Login error:', error);
    errorElement.textContent = error.response?.data?.message || 'Login failed. Please try again.';
    errorElement.classList.remove('d-none');
  }
}

// Logout function
function logout() {
  // Clear localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('admin');

  // Reset state
  state.token = null;
  state.admin = null;
  state.isAuthenticated = false;

  // Remove axios default header
  delete axios.defaults.headers.common['Authorization'];

  // Load login page
  loadLoginPage();

  // Update navigation
  updateNavigation();
}

// Load dashboard
async function loadDashboard() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'dashboard';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get user stats
    const statsResponse = await axios.get(`${API_BASE_URL}/users/stats`);
    const { stats } = statsResponse.data;

    // Get active contests
    const contestsResponse = await axios.get(`${API_BASE_URL}/contests`);
    const { contests } = contestsResponse.data;

    // Filter active contests
    const activeContests = contests.filter(contest => contest.status === 'ACTIVE');

    // Render dashboard
    app.innerHTML = `
      <h1 class="mb-4">Kontrol Paneli</h1>

      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title">Toplam Kullanıcı</h5>
              <p class="card-text display-4">${stats.totalUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title">Yeni Kullanıcılar (7g)</h5>
              <p class="card-text display-4">${stats.newUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title">Aktif Kullanıcılar (7g)</h5>
              <p class="card-text display-4">${stats.activeUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title">Toplam Katılım</h5>
              <p class="card-text display-4">${stats.totalSubmissions}</p>
            </div>
          </div>
        </div>
      </div>

      <h2 class="mb-3">Aktif Yarışmalar</h2>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Başlık</th>
              <th>Bitiş Tarihi</th>
              <th>Sorular</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            ${activeContests.length > 0 ? activeContests.map(contest => `
              <tr>
                <td>${contest.title}</td>
                <td>${new Date(contest.endDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                <td>${contest.questions.length}</td>
                <td>
                  <a href="#" onclick="viewContest('${contest._id}')" class="btn btn-sm btn-primary">Görüntüle</a>
                </td>
              </tr>
            `).join('') : `
              <tr>
                <td colspan="4" class="text-center">Aktif yarışma bulunmuyor</td>
              </tr>
            `}
          </tbody>
        </table>
      </div>

      <div class="text-center mt-4">
        <button class="btn btn-success" onclick="loadCreateContest()">Yeni Yarışma Oluştur</button>
      </div>
    `;
  } catch (error) {
    console.error('Dashboard error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Error loading dashboard. Please try again.
      </div>
    `;
  }
}

// Load contests page
async function loadContests() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'contests';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get contests
    const response = await axios.get(`${API_BASE_URL}/contests`);
    const { contests } = response.data;

    // Group contests by status
    const activeContests = contests.filter(contest => contest.status === 'ACTIVE');
    const draftContests = contests.filter(contest => contest.status === 'DRAFT');
    const completedContests = contests.filter(contest => contest.status === 'COMPLETED');
    const cancelledContests = contests.filter(contest => contest.status === 'CANCELLED');

    // Render contests page
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Yarışmalar</h1>
        <button class="btn btn-success" onclick="loadCreateContest()">Yeni Yarışma Oluştur</button>
      </div>

      <ul class="nav nav-tabs mb-4" id="contestTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
            Aktif (${activeContests.length})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="draft-tab" data-bs-toggle="tab" data-bs-target="#draft" type="button" role="tab">
            Taslak (${draftContests.length})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
            Tamamlanmış (${completedContests.length})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab">
            İptal Edilmiş (${cancelledContests.length})
          </button>
        </li>
      </ul>

      <div class="tab-content" id="contestTabsContent">
        <div class="tab-pane fade show active" id="active" role="tabpanel">
          ${renderContestTable(activeContests)}
        </div>
        <div class="tab-pane fade" id="draft" role="tabpanel">
          ${renderContestTable(draftContests)}
        </div>
        <div class="tab-pane fade" id="completed" role="tabpanel">
          ${renderContestTable(completedContests)}
        </div>
        <div class="tab-pane fade" id="cancelled" role="tabpanel">
          ${renderContestTable(cancelledContests)}
        </div>
      </div>
    `;
  } catch (error) {
    console.error('Contests error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Error loading contests. Please try again.
      </div>
    `;
  }
}

// Render contest table
function renderContestTable(contests) {
  if (contests.length === 0) {
    return `
      <div class="alert alert-info">
        Bu kategoride yarışma bulunamadı.
      </div>
    `;
  }

  return `
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Başlık</th>
            <th>Başlangıç Tarihi</th>
            <th>Bitiş Tarihi</th>
            <th>Sorular</th>
            <th>Oluşturan</th>
            <th>İşlemler</th>
          </tr>
        </thead>
        <tbody>
          ${contests.map(contest => `
            <tr>
              <td>${contest.title}</td>
              <td>${new Date(contest.startDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
              <td>${new Date(contest.endDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
              <td>${contest.questions.length}</td>
              <td>${contest.createdBy.name}</td>
              <td>
                <div class="btn-group">
                  <a href="#" onclick="viewContest('${contest._id}')" class="btn btn-sm btn-primary">Görüntüle</a>
                  ${contest.status !== 'COMPLETED' ? `
                    <a href="#" onclick="editContest('${contest._id}')" class="btn btn-sm btn-warning">Düzenle</a>
                    ${contest.status === 'DRAFT' ? `
                      <a href="#" onclick="activateContest('${contest._id}')" class="btn btn-sm btn-success">Aktifleştir</a>
                    ` : ''}
                  ` : ''}
                  ${contest.status === 'COMPLETED' ? `
                    <a href="#" onclick="viewSubmissions('${contest._id}')" class="btn btn-sm btn-info">Katılımlar</a>
                    <button onclick="exportToExcel('${contest._id}')" class="btn btn-sm btn-success">Dışa Aktar</button>
                  ` : ''}
                </div>
              </td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>
  `;
}

// Load users page
async function loadUsers() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'users';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get users
    const response = await axios.get(`${API_BASE_URL}/users`);
    const { users } = response.data;

    // Render users page
    app.innerHTML = `
      <h1 class="mb-4">Kullanıcılar</h1>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Kullanıcı Adı</th>
              <th>Telegram ID</th>
              <th>İsim</th>
              <th>Kayıt Tarihi</th>
              <th>Son Aktivite</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            ${users.length > 0 ? users.map(user => `
              <tr>
                <td>${user.username}</td>
                <td>${user.telegramId}</td>
                <td>${user.firstName || ''} ${user.lastName || ''}</td>
                <td>${new Date(user.registrationDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric' })}</td>
                <td>${new Date(user.lastActivity).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric' })}</td>
                <td>
                  <a href="#" onclick="viewUser('${user._id}')" class="btn btn-sm btn-primary">Görüntüle</a>
                </td>
              </tr>
            `).join('') : `
              <tr>
                <td colspan="6" class="text-center">Kullanıcı bulunamadı</td>
              </tr>
            `}
          </tbody>
        </table>
      </div>
    `;
  } catch (error) {
    console.error('Users error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Error loading users. Please try again.
      </div>
    `;
  }
}

// Load bot settings page
async function loadSettings() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'settings';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get bot settings
    const response = await axios.get(`${API_BASE_URL}/bot-settings`);
    const { settings } = response.data;

    // Render settings page
    app.innerHTML = `
      <h1 class="mb-4">Bot Ayarları</h1>

      <form id="settings-form">
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Genel Ayarlar</h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label for="botUsername" class="form-label">Bot Kullanıcı Adı</label>
              <input type="text" class="form-control" id="botUsername" value="${settings.botUsername}" required>
            </div>

            <div class="mb-3">
              <label for="welcomeMessage" class="form-label">Hoşgeldin Mesajı</label>
              <textarea class="form-control" id="welcomeMessage" rows="3">${settings.welcomeMessage}</textarea>
            </div>

            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="isActive" ${settings.isActive ? 'checked' : ''}>
              <label class="form-check-label" for="isActive">Bot Aktif</label>
            </div>
          </div>
        </div>

        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Kanal Zorunluluğu</h5>
          </div>
          <div class="card-body">
            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="channelRequired" ${settings.channelRequirement.required ? 'checked' : ''}>
              <label class="form-check-label" for="channelRequired">Kanal Üyeliği Zorunlu</label>
            </div>

            <div class="mb-3">
              <label for="channelUsername" class="form-label">Kanal Kullanıcı Adı</label>
              <input type="text" class="form-control" id="channelUsername" value="${settings.channelRequirement.channelUsername}">
              <div class="form-text">Gerekirse @ sembolünü ekleyin (örn., @kanaliniz)</div>
            </div>
          </div>
        </div>

        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Bakım Modu</h5>
          </div>
          <div class="card-body">
            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="maintenanceMode" ${settings.maintenanceMode ? 'checked' : ''}>
              <label class="form-check-label" for="maintenanceMode">Bakım Modu</label>
            </div>

            <div class="mb-3">
              <label for="maintenanceMessage" class="form-label">Bakım Mesajı</label>
              <textarea class="form-control" id="maintenanceMessage" rows="3">${settings.maintenanceMessage}</textarea>
            </div>
          </div>
        </div>

        <div id="settings-error" class="alert alert-danger d-none"></div>
        <div id="settings-success" class="alert alert-success d-none"></div>

        <button type="submit" class="btn btn-primary">Ayarları Kaydet</button>
      </form>
    `;

    // Add event listener to settings form
    document.getElementById('settings-form').addEventListener('submit', handleSaveSettings);
  } catch (error) {
    console.error('Settings error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Error loading bot settings. Please try again.
      </div>
    `;
  }
}

// Handle save settings form submission
async function handleSaveSettings(e) {
  e.preventDefault();

  const errorElement = document.getElementById('settings-error');
  const successElement = document.getElementById('settings-success');

  try {
    errorElement.classList.add('d-none');
    successElement.classList.add('d-none');

    const settings = {
      botUsername: document.getElementById('botUsername').value,
      welcomeMessage: document.getElementById('welcomeMessage').value,
      isActive: document.getElementById('isActive').checked,
      channelRequirement: {
        required: document.getElementById('channelRequired').checked,
        channelUsername: document.getElementById('channelUsername').value,
      },
      maintenanceMode: document.getElementById('maintenanceMode').checked,
      maintenanceMessage: document.getElementById('maintenanceMessage').value,
    };

    await axios.put(`${API_BASE_URL}/bot-settings`, settings);

    successElement.textContent = 'Settings saved successfully.';
    successElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  } catch (error) {
    console.error('Save settings error:', error);
    errorElement.textContent = error.response?.data?.message || 'Error saving settings. Please try again.';
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// View user details
async function viewUser(userId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get user details
    const response = await axios.get(`${API_BASE_URL}/users/${userId}`);
    const { user, submissions } = response.data;

    // Render user details
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Kullanıcı Detayları</h1>
        <button class="btn btn-secondary" onclick="loadUsers()">Kullanıcı Listesine Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Kullanıcı Bilgileri</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong>Kullanıcı Adı:</strong> ${user.username}</p>
              <p><strong>Telegram ID:</strong> ${user.telegramId}</p>
              <p><strong>İsim:</strong> ${user.firstName || '-'} ${user.lastName || ''}</p>
            </div>
            <div class="col-md-6">
              <p><strong>Kayıt Tarihi:</strong> ${new Date(user.registrationDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric' })}</p>
              <p><strong>Son Aktivite:</strong> ${new Date(user.lastActivity).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric' })}</p>
              <p><strong>Durum:</strong> ${user.state}</p>
            </div>
          </div>
        </div>
      </div>

      <h2 class="mb-3">Katılımlar</h2>

      ${submissions.length > 0 ? `
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Yarışma</th>
                <th>Durum</th>
                <th>Gönderim Tarihi</th>
                <th>Doğru Cevaplar</th>
                <th>Kazanan</th>
                <th>İşlemler</th>
              </tr>
            </thead>
            <tbody>
              ${submissions.map(submission => `
                <tr>
                  <td>${submission.contest.title}</td>
                  <td>${submission.contest.status}</td>
                  <td>${new Date(submission.submittedAt).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                  <td>${submission.correctAnswers !== undefined ? submission.correctAnswers : '-'}</td>
                  <td>${submission.isWinner ? 'Evet' : 'Hayır'}</td>
                  <td>
                    <a href="#" onclick="viewContest('${submission.contest._id}')" class="btn btn-sm btn-primary">Yarışmayı Görüntüle</a>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : `
        <div class="alert alert-info">
          Bu kullanıcı henüz hiçbir yarışmaya katılmamış.
        </div>
      `}
    `;
  } catch (error) {
    console.error('User details error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Kullanıcı detayları yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadUsers()">Kullanıcı Listesine Dön</button>
      </div>
    `;
  }
}

// View contest details
async function viewContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Render contest details
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Yarışma Detayları</h1>
        <button class="btn btn-secondary" onclick="loadContests()">Yarışma Listesine Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">${contest.title}</h5>
          <span class="badge ${getBadgeClass(contest.status)}">${contest.status}</span>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <p><strong>Başlangıç Tarihi:</strong> ${new Date(contest.startDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
              <p><strong>Bitiş Tarihi:</strong> ${new Date(contest.endDate).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
              <p><strong>Oluşturan:</strong> ${contest.createdBy.name}</p>
            </div>
            <div class="col-md-6">
              <p><strong>Soru Sayısı:</strong> ${contest.questions.length}</p>
              <p><strong>Kazanmak için Gereken Doğru Cevap:</strong> ${contest.minCorrectAnswers}</p>
              <p><strong>Kanal Zorunluluğu:</strong> ${contest.channelRequirement.required ? contest.channelRequirement.channelUsername : 'Yok'}</p>
            </div>
          </div>

          <div class="mb-3">
            <h6>Açıklama:</h6>
            <p>${contest.description}</p>
          </div>

          ${contest.requirements ? `
            <div class="mb-3">
              <h6>Katılım Şartları:</h6>
              <p>${contest.requirements}</p>
            </div>
          ` : ''}

          ${contest.prizes ? `
            <div class="mb-3">
              <h6>Ödüller:</h6>
              <p>${contest.prizes}</p>
            </div>
          ` : ''}
        </div>
      </div>

      <h2 class="mb-3">Sorular</h2>

      <div class="accordion mb-4" id="questionsAccordion">
        ${contest.questions.map((question, index) => `
          <div class="accordion-item">
            <h2 class="accordion-header">
              <button class="accordion-button ${index > 0 ? 'collapsed' : ''}" type="button" data-bs-toggle="collapse" data-bs-target="#question${index}">
                Soru ${index + 1}: ${question.text}
              </button>
            </h2>
            <div id="question${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#questionsAccordion">
              <div class="accordion-body">
                <h6>Seçenekler:</h6>
                <ul class="list-group mb-3">
                  ${question.options.map(option => `
                    <li class="list-group-item ${question.correctAnswer === option.value ? 'list-group-item-success' : ''}">
                      ${option.text} ${question.correctAnswer === option.value ? '<span class="badge bg-success float-end">Doğru Cevap</span>' : ''}
                    </li>
                  `).join('')}
                </ul>
              </div>
            </div>
          </div>
        `).join('')}
      </div>

      <div class="d-flex gap-2 mb-4">
        ${contest.status !== 'COMPLETED' ? `
          <button class="btn btn-warning" onclick="editContest('${contest._id}')">Düzenle</button>
          ${contest.status === 'ACTIVE' ? `
            <button class="btn btn-info" onclick="setCorrectAnswers('${contest._id}')">Doğru Cevapları Belirle</button>
            <button class="btn btn-success" onclick="completeContest('${contest._id}')">Yarışmayı Tamamla</button>
          ` : ''}
        ` : ''}

        ${contest.status === 'COMPLETED' ? `
          <button class="btn btn-info" onclick="viewSubmissions('${contest._id}')">Katılımları Görüntüle</button>
          <a href="${API_BASE_URL}/contests/${contest._id}/export" class="btn btn-success">Excel'e Aktar</a>
        ` : ''}
      </div>
    `;
  } catch (error) {
    console.error('Contest details error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Yarışma detayları yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadContests()">Yarışma Listesine Dön</button>
      </div>
    `;
  }
}

// Create new contest
function loadCreateContest() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'create-contest';
  updateNavigation();

  app.innerHTML = `
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1>Yeni Yarışma Oluştur</h1>
      <button class="btn btn-secondary" onclick="loadContests()">Yarışma Listesine Dön</button>
    </div>

    <form id="contest-form">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Yarışma Bilgileri</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="title" class="form-label">Başlık</label>
            <input type="text" class="form-control" id="title" required>
          </div>

          <div class="mb-3">
            <label for="description" class="form-label">Açıklama</label>
            <textarea class="form-control" id="description" rows="3" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                <input type="datetime-local" class="form-control" id="startDate" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="endDate" class="form-label">Bitiş Tarihi</label>
                <input type="datetime-local" class="form-control" id="endDate" required>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="requirements" class="form-label">Katılım Şartları (İsteğe Bağlı)</label>
            <textarea class="form-control" id="requirements" rows="2"></textarea>
          </div>

          <div class="mb-3">
            <label for="prizes" class="form-label">Ödüller (İsteğe Bağlı)</label>
            <textarea class="form-control" id="prizes" rows="2"></textarea>
          </div>

          <div class="mb-3">
            <label for="minCorrectAnswers" class="form-label">Kazanmak için Gereken Minimum Doğru Cevap Sayısı</label>
            <input type="number" class="form-control" id="minCorrectAnswers" min="1" value="1" required>
          </div>

          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="channelRequired">
            <label class="form-check-label" for="channelRequired">Kanal Üyeliği Zorunlu</label>
          </div>

          <div class="mb-3" id="channelUsernameContainer" style="display: none;">
            <label for="channelUsername" class="form-label">Kanal Kullanıcı Adı</label>
            <input type="text" class="form-control" id="channelUsername" placeholder="@kanaladi">
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Sorular</h5>
          <button type="button" class="btn btn-sm btn-primary" id="addQuestionBtn">Soru Ekle</button>
        </div>
        <div class="card-body">
          <div id="questionsContainer">
            <!-- Questions will be added here -->
          </div>

          <div class="alert alert-info" id="noQuestionsAlert">
            Henüz soru eklenmedi. Soru eklemek için "Soru Ekle" butonuna tıklayın.
          </div>
        </div>
      </div>

      <div id="contest-error" class="alert alert-danger d-none"></div>

      <div class="d-flex gap-2">
        <button type="submit" class="btn btn-primary">Yarışmayı Oluştur</button>
        <button type="button" class="btn btn-secondary" onclick="loadContests()">İptal</button>
      </div>
    </form>
  `;

  // Add event listeners
  document.getElementById('channelRequired').addEventListener('change', function () {
    document.getElementById('channelUsernameContainer').style.display = this.checked ? 'block' : 'none';
  });

  document.getElementById('addQuestionBtn').addEventListener('click', addQuestion);
  document.getElementById('contest-form').addEventListener('submit', handleCreateContest);

  // Add first question by default
  addQuestion();
}

// Add question to form
function addQuestion() {
  const questionsContainer = document.getElementById('questionsContainer');
  const noQuestionsAlert = document.getElementById('noQuestionsAlert');

  // Hide no questions alert
  noQuestionsAlert.style.display = 'none';

  // Create question index
  const questionIndex = questionsContainer.children.length;

  // Create question element
  const questionElement = document.createElement('div');
  questionElement.className = 'card mb-3 question-card';
  questionElement.dataset.index = questionIndex;

  questionElement.innerHTML = `
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">Soru ${questionIndex + 1}</h6>
      <button type="button" class="btn btn-sm btn-danger remove-question-btn">Kaldır</button>
    </div>
    <div class="card-body">
      <div class="mb-3">
        <label class="form-label">Soru Metni</label>
        <input type="text" class="form-control question-text" required>
      </div>

      <div class="options-container mb-3">
        <!-- Options will be added here -->
      </div>

      <button type="button" class="btn btn-sm btn-success add-option-btn">Seçenek Ekle</button>
    </div>
  `;

  // Add question to container
  questionsContainer.appendChild(questionElement);

  // Add event listeners
  questionElement.querySelector('.remove-question-btn').addEventListener('click', function () {
    questionElement.remove();
    updateQuestionNumbers();

    // Show no questions alert if no questions
    if (questionsContainer.children.length === 0) {
      noQuestionsAlert.style.display = 'block';
    }
  });

  questionElement.querySelector('.add-option-btn').addEventListener('click', function () {
    addOption(questionElement);
  });

  // Add two options by default
  addOption(questionElement);
  addOption(questionElement);
}

// Add option to question
function addOption(questionElement) {
  const optionsContainer = questionElement.querySelector('.options-container');

  // Create option index
  const optionIndex = optionsContainer.children.length;

  // Create option element
  const optionElement = document.createElement('div');
  optionElement.className = 'input-group mb-2 option-group';
  optionElement.dataset.index = optionIndex;

  optionElement.innerHTML = `
    <span class="input-group-text">Seçenek ${String.fromCharCode(65 + optionIndex)}</span>
    <input type="text" class="form-control option-text" required>
    <button type="button" class="btn btn-outline-danger remove-option-btn">Kaldır</button>
  `;

  // Add option to container
  optionsContainer.appendChild(optionElement);

  // Add event listener
  optionElement.querySelector('.remove-option-btn').addEventListener('click', function () {
    optionElement.remove();
    updateOptionLetters(questionElement);
  });
}

// Update question numbers
function updateQuestionNumbers() {
  const questionCards = document.querySelectorAll('.question-card');

  questionCards.forEach((card, index) => {
    card.dataset.index = index;
    card.querySelector('h6').textContent = `Soru ${index + 1}`;
  });
}

// Update option letters
function updateOptionLetters(questionElement) {
  const optionGroups = questionElement.querySelectorAll('.option-group');

  optionGroups.forEach((group, index) => {
    group.dataset.index = index;
    group.querySelector('.input-group-text').textContent = `Seçenek ${String.fromCharCode(65 + index)}`;
  });
}

// Handle create contest form submission
async function handleCreateContest(e) {
  e.preventDefault();

  const errorElement = document.getElementById('contest-error');
  errorElement.classList.add('d-none');

  try {
    // Validate dates
    const startDateObj = new Date(document.getElementById('startDate').value);
    const endDateObj = new Date(document.getElementById('endDate').value);

    if (endDateObj <= startDateObj) {
      throw new Error('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
    }
    // Get form data
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const requirements = document.getElementById('requirements').value;
    const prizes = document.getElementById('prizes').value;
    const minCorrectAnswers = parseInt(document.getElementById('minCorrectAnswers').value);
    const channelRequired = document.getElementById('channelRequired').checked;
    const channelUsername = document.getElementById('channelUsername').value;

    // Get questions
    const questionCards = document.querySelectorAll('.question-card');
    const questions = [];

    for (const card of questionCards) {
      const questionText = card.querySelector('.question-text').value;
      const optionGroups = card.querySelectorAll('.option-group');

      if (optionGroups.length < 2) {
        throw new Error(`Soru ${parseInt(card.dataset.index) + 1} için en az 2 seçenek gereklidir.`);
      }

      const options = [];

      for (const group of optionGroups) {
        const optionText = group.querySelector('.option-text').value;
        const optionValue = String.fromCharCode(65 + parseInt(group.dataset.index));

        options.push({
          text: optionText,
          value: optionValue,
        });
      }

      questions.push({
        text: questionText,
        options,
      });
    }

    if (questions.length === 0) {
      throw new Error('En az bir soru eklemelisiniz.');
    }

    // Create contest data
    const contestData = {
      title,
      description,
      startDate,
      endDate,
      requirements,
      prizes,
      minCorrectAnswers,
      channelRequirement: {
        required: channelRequired,
        channelUsername: channelRequired ? channelUsername : '',
      },
      questions,
      status: 'DRAFT',
    };

    // Send request
    await axios.post(`${API_BASE_URL}/contests`, contestData);

    // Redirect to contests page
    loadContests();
  } catch (error) {
    console.error('Create contest error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || 'Yarışma oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.';
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// View contest submissions
async function viewSubmissions(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get contest submissions
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}/submissions`);
    const { submissions } = response.data;

    // Get contest details
    const contestResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = contestResponse.data;

    // Render submissions
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Yarışma Katılımları</h1>
        <div>
          <button class="btn btn-secondary me-2" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
          <button onclick="exportToExcel('${contestId}')" class="btn btn-success">Excel'e Aktar</button>
        </div>
      </div>

      <div class="alert alert-info mb-4">
        <h5>${contest.title}</h5>
        <p class="mb-0">Toplam Katılım: ${submissions.length}</p>
      </div>

      ${submissions.length > 0 ? `
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Kullanıcı Adı</th>
                <th>Telegram ID</th>
                <th>İsim</th>
                <th>Doğru Cevaplar</th>
                <th>Kazanan</th>
                <th>Gönderim Tarihi</th>
                <th>İşlemler</th>
              </tr>
            </thead>
            <tbody>
              ${submissions.map(submission => `
                <tr>
                  <td>${submission.user.username}</td>
                  <td>${submission.user.telegramId}</td>
                  <td>${submission.user.firstName || ''} ${submission.user.lastName || ''}</td>
                  <td>${submission.correctAnswers !== undefined ? submission.correctAnswers : '-'}</td>
                  <td>${submission.isWinner ? '<span class="badge bg-success">Evet</span>' : '<span class="badge bg-secondary">Hayır</span>'}</td>
                  <td>${new Date(submission.submittedAt).toLocaleString('tr-TR', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                  <td>
                    <a href="#" onclick="viewUser('${submission.user._id}')" class="btn btn-sm btn-primary">Kullanıcıyı Görüntüle</a>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : `
        <div class="alert alert-warning">
          Bu yarışmaya henüz katılım yok.
        </div>
      `}
    `;
  } catch (error) {
    console.error('Submissions error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Katılımlar yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Helper function to get badge class based on contest status
function getBadgeClass(status) {
  switch (status) {
    case 'ACTIVE':
      return 'bg-success';
    case 'DRAFT':
      return 'bg-secondary';
    case 'COMPLETED':
      return 'bg-primary';
    case 'CANCELLED':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
}

// Edit contest
async function editContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Format dates for datetime-local input with timezone adjustment
    const startDateObj = new Date(contest.startDate);
    const endDateObj = new Date(contest.endDate);

    // Adjust for timezone offset
    const startDate = new Date(startDateObj.getTime() - startDateObj.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    const endDate = new Date(endDateObj.getTime() - endDateObj.getTimezoneOffset() * 60000).toISOString().slice(0, 16);

    // Render edit form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Yarışma Düzenle</h1>
        <button class="btn btn-secondary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>

      <form id="edit-contest-form">
        <div class="card mb-4">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Yarışma Bilgileri</h5>
            <span class="badge ${getBadgeClass(contest.status)}">${contest.status}</span>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label for="title" class="form-label">Başlık</label>
              <input type="text" class="form-control" id="title" value="${contest.title}" required>
            </div>

            <div class="mb-3">
              <label for="description" class="form-label">Açıklama</label>
              <textarea class="form-control" id="description" rows="3" required>${contest.description}</textarea>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                  <input type="datetime-local" class="form-control" id="startDate" value="${startDate}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="endDate" class="form-label">Bitiş Tarihi</label>
                  <input type="datetime-local" class="form-control" id="endDate" value="${endDate}" required>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="requirements" class="form-label">Katılım Şartları (İsteğe Bağlı)</label>
              <textarea class="form-control" id="requirements" rows="2">${contest.requirements || ''}</textarea>
            </div>

            <div class="mb-3">
              <label for="prizes" class="form-label">Ödüller (İsteğe Bağlı)</label>
              <textarea class="form-control" id="prizes" rows="2">${contest.prizes || ''}</textarea>
            </div>

            <div class="mb-3">
              <label for="minCorrectAnswers" class="form-label">Kazanmak için Gereken Minimum Doğru Cevap Sayısı</label>
              <input type="number" class="form-control" id="minCorrectAnswers" min="1" value="${contest.minCorrectAnswers}" required>
            </div>

            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="channelRequired" ${contest.channelRequirement.required ? 'checked' : ''}>
              <label class="form-check-label" for="channelRequired">Kanal Üyeliği Zorunlu</label>
            </div>

            <div class="mb-3" id="channelUsernameContainer" style="display: ${contest.channelRequirement.required ? 'block' : 'none'};">
              <label for="channelUsername" class="form-label">Kanal Kullanıcı Adı</label>
              <input type="text" class="form-control" id="channelUsername" placeholder="@kanaladi" value="${contest.channelRequirement.channelUsername || ''}">
            </div>

            ${contest.status === 'DRAFT' ? `
              <div class="mb-3">
                <label for="status" class="form-label">Durum</label>
                <select class="form-select" id="status">
                  <option value="DRAFT" ${contest.status === 'DRAFT' ? 'selected' : ''}>Taslak</option>
                  <option value="ACTIVE" ${contest.status === 'ACTIVE' ? 'selected' : ''}>Aktif</option>
                </select>
              </div>
            ` : ''}
          </div>
        </div>

        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Sorular</h5>
          </div>
          <div class="card-body">
            <div id="questionsContainer">
              ${contest.questions.map((question, qIndex) => `
                <div class="card mb-3 question-card" data-index="${qIndex}">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Soru ${qIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-question-btn">Kaldır</button>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label class="form-label">Soru Metni</label>
                      <input type="text" class="form-control question-text" value="${question.text}" required>
                    </div>

                    <div class="options-container mb-3">
                      ${question.options.map((option, oIndex) => `
                        <div class="input-group mb-2 option-group" data-index="${oIndex}">
                          <span class="input-group-text">Seçenek ${String.fromCharCode(65 + oIndex)}</span>
                          <input type="text" class="form-control option-text" value="${option.text}" required>
                          <button type="button" class="btn btn-outline-danger remove-option-btn">Kaldır</button>
                        </div>
                      `).join('')}
                    </div>

                    <button type="button" class="btn btn-sm btn-success add-option-btn">Seçenek Ekle</button>
                  </div>
                </div>
              `).join('')}
            </div>

            <button type="button" class="btn btn-primary mt-3" id="addQuestionBtn">Soru Ekle</button>
          </div>
        </div>

        <div id="edit-contest-error" class="alert alert-danger d-none"></div>

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary">Değişiklikleri Kaydet</button>
          <button type="button" class="btn btn-secondary" onclick="viewContest('${contestId}')">İptal</button>
        </div>
      </form>
    `;

    // Add event listeners
    document.getElementById('channelRequired').addEventListener('change', function () {
      document.getElementById('channelUsernameContainer').style.display = this.checked ? 'block' : 'none';
    });

    document.getElementById('addQuestionBtn').addEventListener('click', addQuestion);

    // Add event listeners to existing questions and options
    document.querySelectorAll('.remove-question-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const questionCard = this.closest('.question-card');
        questionCard.remove();
        updateQuestionNumbers();
      });
    });

    document.querySelectorAll('.add-option-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const questionCard = this.closest('.question-card');
        addOption(questionCard);
      });
    });

    document.querySelectorAll('.remove-option-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const optionGroup = this.closest('.option-group');
        const questionCard = this.closest('.question-card');
        optionGroup.remove();
        updateOptionLetters(questionCard);
      });
    });

    // Add submit event listener
    document.getElementById('edit-contest-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleEditContest(e, contestId);
    });
  } catch (error) {
    console.error('Edit contest error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Yarışma düzenleme formu yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Handle edit contest form submission
async function handleEditContest(e, contestId) {
  e.preventDefault();

  const errorElement = document.getElementById('edit-contest-error');
  errorElement.classList.add('d-none');

  try {
    // Validate dates
    const startDateObj = new Date(document.getElementById('startDate').value);
    const endDateObj = new Date(document.getElementById('endDate').value);

    if (endDateObj <= startDateObj) {
      throw new Error('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
    }
    // Get form data
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const requirements = document.getElementById('requirements').value;
    const prizes = document.getElementById('prizes').value;
    const minCorrectAnswers = parseInt(document.getElementById('minCorrectAnswers').value);
    const channelRequired = document.getElementById('channelRequired').checked;
    const channelUsername = document.getElementById('channelUsername').value;
    const statusElement = document.getElementById('status');
    const status = statusElement ? statusElement.value : undefined;

    // Get questions
    const questionCards = document.querySelectorAll('.question-card');
    const questions = [];

    for (const card of questionCards) {
      const questionText = card.querySelector('.question-text').value;
      const optionGroups = card.querySelectorAll('.option-group');

      if (optionGroups.length < 2) {
        throw new Error(`Soru ${parseInt(card.dataset.index) + 1} için en az 2 seçenek gereklidir.`);
      }

      const options = [];

      for (const group of optionGroups) {
        const optionText = group.querySelector('.option-text').value;
        const optionValue = String.fromCharCode(65 + parseInt(group.dataset.index));

        options.push({
          text: optionText,
          value: optionValue,
        });
      }

      questions.push({
        text: questionText,
        options,
      });
    }

    if (questions.length === 0) {
      throw new Error('En az bir soru eklemelisiniz.');
    }

    // Create contest data
    const contestData = {
      title,
      description,
      startDate,
      endDate,
      requirements,
      prizes,
      minCorrectAnswers,
      channelRequirement: {
        required: channelRequired,
        channelUsername: channelRequired ? channelUsername : '',
      },
      questions,
    };

    // Add status if available
    if (status) {
      contestData.status = status;
    }

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}`, contestData);

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Edit contest error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || 'Yarışma güncellenirken bir hata oluştu. Lütfen tekrar deneyin.';
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// Set correct answers for a contest
async function setCorrectAnswers(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Render correct answers form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Doğru Cevapları Belirle</h1>
        <button class="btn btn-secondary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>

      <div class="alert alert-info mb-4">
        <h5>${contest.title}</h5>
        <p>Her soru için doğru cevabı seçin. Bu işlem, yarışma tamamlandıktan sonra kazananları belirlemek için kullanılacaktır.</p>
      </div>

      <form id="correct-answers-form">
        ${contest.questions.map((question, index) => `
          <div class="card mb-3">
            <div class="card-header">
              <h5 class="mb-0">Soru ${index + 1}: ${question.text}</h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label class="form-label">Doğru Cevap:</label>
                <select class="form-select correct-answer" data-question-index="${index}">
                  <option value="">Seçiniz...</option>
                  ${question.options.map(option => `
                    <option value="${option.value}" ${question.correctAnswer === option.value ? 'selected' : ''}>
                      ${option.text} (${option.value})
                    </option>
                  `).join('')}
                </select>
              </div>
            </div>
          </div>
        `).join('')}

        <div id="correct-answers-error" class="alert alert-danger d-none"></div>

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary">Doğru Cevapları Kaydet</button>
          <button type="button" class="btn btn-secondary" onclick="viewContest('${contestId}')">İptal</button>
        </div>
      </form>
    `;

    // Add submit event listener
    document.getElementById('correct-answers-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleSetCorrectAnswers(e, contestId, contest.questions.length);
    });
  } catch (error) {
    console.error('Set correct answers error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Doğru cevaplar formu yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Handle set correct answers form submission
async function handleSetCorrectAnswers(e, contestId, questionCount) {
  e.preventDefault();

  const errorElement = document.getElementById('correct-answers-error');
  errorElement.classList.add('d-none');

  try {
    // Get correct answers
    const correctAnswers = [];
    let hasEmptyAnswer = false;

    for (let i = 0; i < questionCount; i++) {
      const select = document.querySelector(`.correct-answer[data-question-index="${i}"]`);
      const value = select.value;

      if (!value) {
        hasEmptyAnswer = true;
      }

      correctAnswers.push(value);
    }

    if (hasEmptyAnswer) {
      throw new Error('Lütfen tüm sorular için doğru cevapları belirleyin.');
    }

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}/set-answers`, { correctAnswers });

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Set correct answers error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || 'Doğru cevaplar kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.';
    errorElement.classList.remove('d-none');
  }
}

// Export contest submissions to Excel
async function exportToExcel(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  try {
    // Get the token from state
    const token = state.token;

    // Create a download link
    const downloadLink = document.createElement('a');
    downloadLink.href = `${API_BASE_URL}/contests/${contestId}/export`;

    // Open in a new window with the token in the URL
    window.open(`${API_BASE_URL}/contests/${contestId}/export?token=${token}`, '_blank');
  } catch (error) {
    console.error('Export error:', error);
    alert('Excel\'e aktarma işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.');
  }
}
if (!state.isAuthenticated) {
  return loadLoginPage();
}

if (!confirm('Bu yarışmayı aktifleştirmek istediğinizden emin misiniz?')) {
  return;
}

try {
  // Show loading
  app.innerHTML = `
      < div class="text-center" >
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Yarışma aktifleştiriliyor...</p>
      </div >
      `;

  // Send request
  await axios.put(`${API_BASE_URL} /contests/${contestId}/activate`);

  // Redirect to contest details
  viewContest(contestId);
} catch (error) {
  console.error('Activate contest error:', error);
  app.innerHTML = `
      <div class="alert alert-danger">
        Yarışma aktifleştirilirken bir hata oluştu: ${error.response?.data?.message || error.message || 'Bilinmeyen bir hata'}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
}
}

// Complete contest
async function completeContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  if (!confirm('Bu yarışmayı tamamlamak istediğinizden emin misiniz? Bu işlem geri alınamaz ve kazananlar belirlenecektir.')) {
    return;
  }

  try {
    // Show loading
    app.innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Yarışma tamamlanıyor ve kazananlar hesaplanıyor...</p>
      </div>
    `;

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}/complete`);

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Complete contest error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Yarışma tamamlanırken bir hata oluştu: ${error.response?.data?.message || error.message || 'Bilinmeyen bir hata'}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Initialize the app
init();
