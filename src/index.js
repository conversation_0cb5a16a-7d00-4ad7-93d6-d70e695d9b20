const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// JWT Secret kontrolü ve otomatik oluşturma
const ensureJwtSecret = require('./utils/ensureJwtSecret');
ensureJwtSecret();

// Import database connection
const connectDB = require('./config/db');

// Import bot service
const botService = require('./bot/bot');

// Import BotSettings model
const BotSettings = require('./models/BotSettings');

// Create Express app
const app = express();

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, '../public')));

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Serve locales
app.use('/locales', express.static(path.join(__dirname, '../public/locales')));

// Import routes
const authRoutes = require('./routes/auth.routes');
const contestRoutes = require('./routes/contest.routes');
const userRoutes = require('./routes/user.routes');
const botSettingsRoutes = require('./routes/botSettings.routes');
const adminRoutes = require('./routes/admin.routes');
const notificationRoutes = require('./routes/notification.routes');
const shortUrlRoutes = require('./routes/shortUrl.routes');

// Use routes
app.use('/api/auth', authRoutes);
app.use('/api/contests', contestRoutes);
app.use('/api/users', userRoutes);
app.use('/api/bot-settings', botSettingsRoutes);
app.use('/api/admins', adminRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/u', shortUrlRoutes); // Kısa URL yönlendirme için

// Serve the frontend for any other route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/index.html', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Bildirim ayarlarını .env dosyasından güncelle
const updateNotificationSettings = async () => {
  try {
    console.log('Checking notification settings from .env file...');
    await BotSettings.getSettings();
    console.log('Notification settings check completed');
  } catch (error) {
    console.error('Error updating notification settings:', error);
  }
};

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);

  // Bildirim ayarlarını güncelle
  await updateNotificationSettings();

  // Start the Telegram bot
  botService.startBot();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});
