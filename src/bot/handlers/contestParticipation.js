const Contest = require('../../models/Contest');
const Submission = require('../../models/Submission');
const { checkChannelMembership, formatContestDetails, createOptionsKeyboard, createConfirmationKeyboard } = require('../utils');
const { getTranslation } = require('../translations');

// Sayıları emoji olarak döndüren yardımcı fonksiyon
const getNumberEmoji = (number) => {
  const numberEmojis = ['0️⃣', '1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'];

  if (number >= 0 && number <= 10) {
    return numberEmojis[number];
  }

  // 10'dan büyük sayılar için normal sayı döndür
  return number;
};

// Handle contest participation
const handleContestParticipation = async (bot, callbackQuery, user, contestId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get contest
    const contest = await Contest.findById(contestId);

    if (!contest || contest.status !== 'ACTIVE') {
      const translation = await getTranslation(user.language, 'bot.participation.notFound');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Check if contest is still open
    const now = new Date();
    if (now < contest.startDate || now > contest.endDate) {
      const translation = await getTranslation(user.language, 'bot.participation.notActive');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Check if user already submitted
    const existingSubmission = await Submission.findOne({
      user: user._id,
      contest: contestId,
    });

    if (existingSubmission) {
      const translation = await getTranslation(user.language, 'bot.participation.alreadySubmitted');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Check channel membership if required
    if (contest.channelRequirement && contest.channelRequirement.required) {
      const isMember = await checkChannelMembership(
        bot,
        user.telegramId,
        contest.channelRequirement.channelUsername
      );

      if (!isMember) {
        const translation = await getTranslation(user.language, 'bot.participation.channelRequired', { channel: contest.channelRequirement.channelUsername });
        return bot.sendMessage(
          chatId,
          translation
        );
      }
    }

    // Update user state
    user.state = 'ANSWERING';
    user.currentContest = contestId;
    user.currentQuestionIndex = 0;
    user.tempAnswers = new Map();
    await user.save();

    // Show contest details
    const message = await formatContestDetails(contest, user.language);

    // Send contest details and wait for it to complete before sending the first question
    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
    });

    // Add a small delay to ensure messages are received in the correct order
    await new Promise(resolve => setTimeout(resolve, 500));

    // Show first question
    await sendQuestion(bot, chatId, user, contest);
  } catch (error) {
    console.error('Error handling contest participation:', error);
    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(
      chatId,
      translation
    );
  }
};

// Send current question to user
const sendQuestion = async (bot, chatId, user, contest) => {
  try {
    const questionIndex = user.currentQuestionIndex;

    // Check if all questions are answered
    if (questionIndex >= contest.questions.length) {
      // All questions answered, show summary
      return await showAnswerSummary(bot, chatId, user, contest);
    }

    const question = contest.questions[questionIndex];

    // Create message with emojis
    const translation = await getTranslation(user.language, 'bot.answering.question', {
      current: questionIndex + 1,
      total: contest.questions.length,
      text: question.text
    });

    const selectPrompt = await getTranslation(user.language, 'bot.answering.selectPrompt');

    // Emojileri zenginleştirelim
    const questionEmoji = "❓"; // Soru için emoji
    const selectEmoji = "🔍"; // Seçim için emoji
    const numberEmoji = getNumberEmoji(questionIndex + 1); // Soru numarası için emoji

    let message = `${questionEmoji} ${numberEmoji} *${translation}*\n\n${selectEmoji} ${selectPrompt}`;

    // Create keyboard with options
    const keyboard = await createOptionsKeyboard(question);

    // Mesajı gönder ve tamamlanmasını bekle
    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard,
    });
  } catch (error) {
    console.error('Error sending question:', error);
    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(
      chatId,
      `❌ ${translation}`
    );
  }
};

// Show answer summary
const showAnswerSummary = async (bot, chatId, user, contest) => {
  try {
    // Update user state
    user.state = 'CONFIRMING';
    await user.save();

    // Create summary message with emojis
    const summaryTitle = await getTranslation(user.language, 'bot.answering.summaryTitle', { title: contest.title });
    let message = `📋 *${summaryTitle}*\n\n`;

    // Process each question
    for (let index = 0; index < contest.questions.length; index++) {
      const question = contest.questions[index];
      const questionId = question._id.toString();
      const answer = user.tempAnswers.get(questionId);

      // Find option text for the answer
      let answerText = '';
      let answerEmoji = '⚠️'; // Cevap verilmemiş emoji

      // Cevap verilmemiş durumu için çeviri
      const noAnswerText = await getTranslation(user.language, 'bot.answering.noAnswer');

      if (answer) {
        const option = question.options.find(opt => opt.value === answer);
        if (option) {
          answerText = option.text;
          answerEmoji = '✅'; // Cevap verilmiş emoji
        } else {
          answerText = noAnswerText;
        }
      } else {
        answerText = noAnswerText;
      }

      // Soru numarası için emoji
      const numberEmoji = getNumberEmoji(index + 1);

      // Soru metni için çeviri
      const questionLabel = await getTranslation(user.language, 'bot.answering.questionLabel', { number: index + 1 });
      const answerLabel = await getTranslation(user.language, 'bot.answering.answerLabel');

      message += `${numberEmoji} *${questionLabel}* ${question.text}\n`;
      message += `${answerEmoji} *${answerLabel}* ${answerText}\n\n`;
    }

    const confirmPrompt = await getTranslation(user.language, 'bot.answering.confirmQuestion');
    message += `🤔 ${confirmPrompt}`;

    // Create confirmation keyboard
    const keyboard = await createConfirmationKeyboard(user.language);

    // Mesajı gönder ve tamamlanmasını bekle
    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard,
    });
  } catch (error) {
    console.error('Error showing answer summary:', error);
    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(
      chatId,
      `❌ ${translation}`
    );
  }
};

module.exports = {
  handleContestParticipation,
  sendQuestion,
};
