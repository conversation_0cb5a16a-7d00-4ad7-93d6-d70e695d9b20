const User = require('../../models/User');
const BotSettings = require('../../models/BotSettings');
const { getTranslation } = require('../translations');

// Handle /start command
const handleStart = async (bot, msg) => {
  const chatId = msg.chat.id;
  const telegramId = msg.from.id.toString();

  try {
    // Get bot settings
    const settings = await BotSettings.getSettings();

    // Check if user already exists
    let user = await User.findOne({ telegramId });

    if (user) {
      // User already registered
      user.lastActivity = Date.now();
      await user.save();

      // Get welcome back message
      const welcomeMessage = await getTranslation(user.language, 'bot.start.welcomeBack', { username: user.username });

      bot.sendMessage(
        chatId,
        welcomeMessage
      );
    } else {
      // Generate a temporary username if not provided
      const tempUsername = msg.from.username || `user_${telegramId.substring(0, 8)}`;

      // New user, create record
      user = await User.create({
        telegramId,
        username: tempUsername,
        firstName: msg.from.first_name || '',
        lastName: msg.from.last_name || '',
        state: 'REGISTERING',
      });

      // Send welcome message with Markdown formatting
      bot.sendMessage(chatId, settings.welcomeMessage, { parse_mode: 'Markdown' });

      // Ask for username
      const registerMessage = await getTranslation(user.language, 'bot.start.register');

      bot.sendMessage(
        chatId,
        registerMessage
      );
    }
  } catch (error) {
    console.error('Error handling start command:', error);
    // Default to Turkish if user is not found
    const language = 'tr'; // Hata durumunda varsayılan dil Türkçe
    const errorMessage = await getTranslation(language, 'bot.errors.general');

    bot.sendMessage(
      chatId,
      errorMessage
    );
  }
};

module.exports = { handleStart };
