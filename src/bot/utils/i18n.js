/**
 * Get localized message based on user language
 * @param {Object} user - User object with language property
 * @param {Object} messages - Object with messages for each language
 * @returns {String} - Localized message
 */
const getLocalizedMessage = (user, messages) => {
  if (!user || !messages) {
    return messages.tr || '';
  }
  
  const language = user.language || 'tr';
  return messages[language] || messages.tr || '';
};

module.exports = { getLocalizedMessage };
