<!DOCTYPE html>
<html lang="tr">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bildirim Yönetimi - Spor Tahmin <PERSON></title>
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
      <a class="navbar-brand" href="index.html">Spor Tahmin <PERSON></a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ml-auto">
          <li class="nav-item">
            <a class="nav-link" href="dashboard.html">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="contests.html">
              <i class="fas fa-trophy"></i> Yarışmalar
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="users.html">
              <i class="fas fa-users"></i> Kullanıcılar
            </a>
          </li>
          <li class="nav-item active">
            <a class="nav-link" href="notifications.html">
              <i class="fas fa-bell"></i> Bildirimler
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="settings.html">
              <i class="fas fa-cog"></i> Ayarlar
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" id="logoutBtn">
              <i class="fas fa-sign-out-alt"></i> Çıkış
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Ana İçerik -->
  <div class="container mt-4">
    <div id="alertsContainer"></div>

    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="fas fa-bell"></i> Bildirim Yönetimi</h2>
      <div>
        <button class="btn btn-primary" data-toggle="modal" data-target="#createNotificationModal">
          <i class="fas fa-plus"></i> Yeni Bildirim
        </button>
        <button class="btn btn-secondary ml-2" id="refreshNotificationsBtn">
          <i class="fas fa-sync-alt"></i> Yenile
        </button>
      </div>
    </div>

    <!-- Filtreler -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="statusFilter">Durum</label>
              <select class="form-control" id="statusFilter">
                <option value="">Tümü</option>
                <option value="DRAFT">Taslak</option>
                <option value="SCHEDULED">Zamanlandı</option>
                <option value="SENDING">Gönderiliyor</option>
                <option value="PAUSED">Duraklatıldı</option>
                <option value="COMPLETED">Tamamlandı</option>
                <option value="FAILED">Başarısız</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bildirim Listesi -->
    <div class="card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Bildirimler</h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Başlık</th>
                <th>Durum</th>
                <th>Toplam Alıcı</th>
                <th>Başarılı</th>
                <th>Başarısız</th>
                <th>Oluşturulma Tarihi</th>
                <th>İşlemler</th>
              </tr>
            </thead>
            <tbody id="notificationsTableBody">
              <!-- Bildirimler buraya yüklenecek -->
            </tbody>
          </table>
        </div>

        <!-- Sayfalama -->
        <div id="notificationsPagination" class="mt-3"></div>
      </div>
    </div>
  </div>

  <!-- Yeni Bildirim Oluşturma Modal -->
  <div class="modal fade" id="createNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">Yeni Bildirim Oluştur</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="createNotificationForm" enctype="multipart/form-data" onsubmit="return false;">
            <div class="form-group">
              <label for="notificationTitle">Başlık</label>
              <input type="text" class="form-control" id="notificationTitle" required>
              <small class="form-text text-muted">Bildirim başlığı (sadece yönetim panelinde görünür)</small>
            </div>

            <div class="form-group">
              <label for="notificationMessage">Mesaj</label>
              <textarea class="form-control" id="notificationMessage" rows="5" required></textarea>
              <small class="form-text text-muted">
                Telegram Markdown formatını kullanabilirsiniz.
                <br>
                <strong>Örnek:</strong> *Kalın*, _İtalik_, [Link](https://example.com), `Kod`
              </small>
            </div>

            <div class="form-group">
              <label for="notificationImage">Fotoğraf (İsteğe Bağlı)</label>
              <input type="file" class="form-control" id="notificationImage" accept="image/jpeg,image/png,image/gif">
              <small class="form-text text-muted">
                Maksimum dosya boyutu: 5MB. İzin verilen formatlar: JPG, PNG, GIF
              </small>
              <div id="imagePreviewContainer" class="mt-2 d-none">
                <img id="imagePreview" class="img-fluid img-thumbnail" style="max-height: 200px;">
                <button type="button" class="btn btn-sm btn-danger mt-1" id="removeImageBtn">Fotoğrafı Kaldır</button>
              </div>
            </div>

            <div class="form-group">
              <label>Butonlar</label>
              <div id="notificationButtons" class="mb-2">
                <!-- Butonlar buraya eklenecek -->
              </div>
              <button type="button" class="btn btn-sm btn-secondary" id="addButtonBtn">
                <i class="fas fa-plus"></i> Buton Ekle
              </button>
              <small class="form-text text-muted">
                Bildirime eklemek istediğiniz butonları ekleyin (maksimum 10 buton). Her buton için bir metin ve URL
                belirtmelisiniz.
              </small>
            </div>

            <div class="form-group">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="previewBeforeSend" checked>
                <label class="form-check-label" for="previewBeforeSend">
                  Göndermeden önce önizle
                </label>
              </div>
            </div>

            <div id="notificationError" class="alert alert-danger d-none"></div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">İptal</button>
          <button type="button" class="btn btn-primary" id="createNotificationBtn">Oluştur</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bildirim Detayları Modal -->
  <div class="modal fade" id="notificationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">Bildirim Detayları</h5>
          <button type="button" class="close text-white" data-dismiss="modal">
            <span>&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <!-- Bildirim detayları buraya yüklenecek -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Kapat</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bildirim Önizleme Modal -->
  <div class="modal fade" id="previewNotificationModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">Bildirim Önizleme</h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="card mb-3">
            <div class="card-header bg-light">
              <strong>Mesaj Önizleme</strong>
            </div>
            <div class="card-body">
              <div id="previewMessageContent"></div>
            </div>
          </div>

          <div id="previewImageContainer" class="d-none mb-3">
            <div class="card">
              <div class="card-header bg-light">
                <strong>Fotoğraf Önizleme</strong>
              </div>
              <div class="card-body text-center">
                <img id="previewImage" class="img-fluid img-thumbnail" style="max-height: 300px;">
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Geri</button>
          <button type="button" class="btn btn-primary" id="confirmSendBtn">Gönder</button>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/notifications.js"></script>
</body>

</html>