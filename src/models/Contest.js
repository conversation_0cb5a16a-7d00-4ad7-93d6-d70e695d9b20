const mongoose = require('mongoose');

const OptionSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
});

const QuestionSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true,
  },
  options: [OptionSchema],
  correctAnswer: {
    type: String,
  },
});

const ContestSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    // Contest type: 'SPORTS_PREDICTION' or 'EMOJI_GAME'
    type: {
      type: String,
      enum: ['SPORTS_PREDICTION', 'EMOJI_GAME'],
      default: 'SPORTS_PREDICTION',
    },
    // For sports prediction contests
    questions: [QuestionSchema],
    minCorrectAnswers: {
      type: Number,
      default: 1,
      min: 1,
    },
    // For emoji game contests
    emojiGame: {
      gameType: {
        type: String,
        enum: ['DICE', 'BASKETBALL', 'FOOTBALL', 'DART', 'BOWLING', 'SLOT'],
      },
      attemptCount: {
        type: Number,
        min: 1,
        max: 50,
      },
      // For dice games
      diceSettings: {
        targetType: {
          type: String,
          enum: ['TOTAL_VALUE', 'SPECIFIC_VALUE_COUNT'],
        },
        targetValue: Number, // minimum total value or target value (like 6)
        targetCount: Number, // how many times the target value should appear
      },
      // For basketball/football
      successTarget: {
        type: Number,
        min: 1,
      },
      // For dart
      bullseyeTarget: {
        type: Number,
        min: 1,
      },
      // For bowling
      strikeTarget: {
        type: Number,
        min: 1,
      },
      // For slot machine
      slotSettings: {
        winningCombinations: [{
          combination: [String], // Array of 3 emojis
          name: String,
        }],
      },
    },
    requirements: {
      type: String,
      default: '',
    },
    prizes: {
      type: String,
      default: '',
    },
    status: {
      type: String,
      enum: ['DRAFT', 'ACTIVE', 'COMPLETED', 'CANCELLED'],
      default: 'ACTIVE',
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      required: true,
    },
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
    },
    cancelledAt: {
      type: Date,
    },
    cancelReason: {
      type: String,
      default: '',
    },
    channelRequirement: {
      required: {
        type: Boolean,
        default: false,
      },
      channelUsername: {
        type: String,
        default: '',
      },
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model('Contest', ContestSchema);
