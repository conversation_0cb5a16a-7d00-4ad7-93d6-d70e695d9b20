const Notification = require('../models/Notification');
const User = require('../models/User');
const BotSettings = require('../models/BotSettings');
const ShortUrl = require('../models/ShortUrl');
const botService = require('../bot/bot');
const fs = require('fs');
const path = require('path');

/**
 * Bildirim oluşturma
 */
const createNotification = async (notificationData, adminId, imageFile = null) => {
  try {
    // URL'ler artık kullanılmıyor
    const urls = [];

    // Butonları işle
    const buttons = [];
    if (notificationData.buttons && notificationData.buttons.length > 0) {
      for (const button of notificationData.buttons) {
        buttons.push({
          text: button.text,
          url: button.url,
          clicks: 0,
          clickedBy: []
        });
      }
    }

    // Alıcıları hazırla
    let recipients = [];

    // Eğer belirli alıcılar belirtilmişse onları kullan
    if (notificationData.recipients && Array.isArray(notificationData.recipients) && notificationData.recipients.length > 0) {
      // Belirli kullanıcılara gönder
      recipients = notificationData.recipients.map(userId => ({
        user: userId,
        status: 'PENDING'
      }));
    } else {
      // Tüm kullanıcılara gönder
      const users = await User.find({ isAdmin: false }).select('_id');
      recipients = users.map(user => ({
        user: user._id,
        status: 'PENDING'
      }));
    }

    // Fotoğraf işleme
    let imagePath = null;
    if (imageFile) {
      try {
        // Dosya adını güvenli hale getir
        const timestamp = Date.now();
        const safeFileName = `${timestamp}_${imageFile.originalname.replace(/[^a-zA-Z0-9.]/g, '_')}`;

        // Uploads klasörünü kontrol et ve yoksa oluştur
        const uploadsDir = path.join(process.cwd(), 'src/uploads/notifications');
        console.log("Uploads dizini:", uploadsDir);

        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
          console.log("Uploads dizini oluşturuldu");
        }

        // Dosyayı kaydet
        const fullImagePath = path.join(uploadsDir, safeFileName);
        console.log("Dosya kaydediliyor:", fullImagePath);
        await fs.promises.writeFile(fullImagePath, imageFile.buffer);
        console.log("Dosya başarıyla kaydedildi");

        // Veritabanında saklanacak göreceli yol
        imagePath = `/src/uploads/notifications/${safeFileName}`;
        console.log("Veritabanına kaydedilecek yol:", imagePath);
      } catch (error) {
        console.error("Fotoğraf kaydetme hatası:", error);
        throw new Error(`Fotoğraf yüklenirken hata oluştu: ${error.message}`);
      }
    }

    // Bildirim oluştur
    const notification = await Notification.create({
      ...notificationData,
      image: imagePath,
      urls,
      buttons,
      createdBy: adminId,
      totalRecipients: recipients.length,
      pendingCount: recipients.length,
      recipients,
      // Eğer belirli alıcılar belirtilmişse (yarışma kazananları gibi) direkt SENDING durumunda oluştur
      status: notificationData.recipients && Array.isArray(notificationData.recipients) && notificationData.recipients.length > 0 ? 'SENDING' : 'DRAFT'
    });

    return notification;
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Bildirim önizleme
 */
const previewNotification = async (notificationId) => {
  try {
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Mesajı hazırla
    let message = notification.message;

    // Butonları hazırla
    let buttons = [];
    if (notification.buttons && notification.buttons.length > 0) {
      notification.buttons.forEach(button => {
        buttons.push({
          text: button.text,
          url: button.url
        });
      });
    }

    return {
      title: notification.title,
      message,
      buttons,
      image: notification.image
    };
  } catch (error) {
    console.error('Bildirim önizleme hatası:', error);
    throw error;
  }
};

/**
 * Bildirim gönderme
 */
const sendNotification = async (notificationId) => {
  try {
    // Bildirim verisini ve kullanıcı bilgilerini al
    const notification = await Notification.findById(notificationId)
      .populate('recipients.user', 'telegramId username');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Bildirim durumunu güncelle
    await notification.updateStatus('SENDING');

    // Bot instance'ını al
    const bot = botService.getBot();
    if (!bot) {
      throw new Error('Bot başlatılamadı');
    }

    // Ayarları al
    const settings = await BotSettings.getSettings();
    const batchSize = settings.notifications.batchSize || 10; // Varsayılan değer
    const delayBetweenBatches = settings.notifications.delayBetweenBatches || 3000; // Varsayılan değer

    console.log(`Bildirim gönderimi başlatılıyor. Toplam alıcı: ${notification.totalRecipients}, Batch size: ${batchSize}, Delay: ${delayBetweenBatches}ms`);

    // Gönderim işlemini başlat
    processBatch(notification, bot, 0, batchSize, delayBetweenBatches);

    return notification;
  } catch (error) {
    console.error('Bildirim gönderme hatası:', error);

    // Hata durumunda bildirim durumunu güncelle
    try {
      const notification = await Notification.findById(notificationId);
      if (notification) {
        await notification.updateStatus('FAILED');
      }
    } catch (innerError) {
      console.error('Hata durumunda bildirim güncelleme hatası:', innerError);
    }

    throw error;
  }
};

/**
 * Bildirim gönderim işlemini toplu olarak işle
 */
const processBatch = async (notification, bot, startIndex, batchSize, delayBetweenBatches) => {
  try {
    // Bildirim durumunu kontrol et
    const freshNotification = await Notification.findById(notification._id);
    if (freshNotification.status !== 'SENDING') {
      console.log(`Bildirim gönderimi durduruldu. Durum: ${freshNotification.status}`);
      return;
    }

    // Taze bildirim verisi al ve kullanıcı bilgilerini populate et
    const updatedNotification = await Notification.findById(notification._id)
      .populate('recipients.user', 'telegramId username');

    if (!updatedNotification) {
      console.error('Bildirim bulunamadı');
      return;
    }

    const endIndex = Math.min(startIndex + batchSize, updatedNotification.recipients.length);
    const batch = updatedNotification.recipients.slice(startIndex, endIndex);

    // Mesajı hazırla
    let message = updatedNotification.message;

    // Sabit değerler
    const MAX_RETRY_COUNT = 3;

    // Toplu gönderim
    for (let batchIndex = 0; batchIndex < batch.length; batchIndex++) {
      const recipient = batch[batchIndex];
      // Gerçek indeksi hesapla (startIndex + batchIndex)
      const recipientIndex = startIndex + batchIndex;

      // Sadece PENDING durumundaki alıcılara gönder
      if (recipient.status !== 'PENDING') {
        console.log(`Alıcı #${recipientIndex} durumu ${recipient.status} olduğu için atlanıyor.`);
        continue;
      }

      // RETRY durumundaki alıcıları kontrol eden kodu kaldırdık
      // Artık yeniden deneme mekanizması yok, direkt olarak FAILED durumuna geçiyoruz

      try {
        // Kullanıcı bilgilerini kontrol et
        if (!recipient.user) {
          console.error('Geçersiz kullanıcı verisi (user yok):', recipient);
          continue;
        }

        // Kullanıcı ID'sini güvenli bir şekilde al
        const userId = recipient.user._id || recipient.user;
        if (!userId) {
          console.error('Geçersiz kullanıcı verisi (userId yok):', recipient);
          continue;
        }

        // Kullanıcı ID'sini string'e çevir
        const userIdStr = String(userId);

        // Telegram ID ve kullanıcı adını al
        const telegramId = recipient.user.telegramId;
        const username = recipient.user.username || 'Bilinmeyen Kullanıcı';
        const retryCount = recipient.retryCount || 0;

        console.log(`İşlenen kullanıcı: ${username}, ID: ${userIdStr}, Telegram ID: ${telegramId}, Alıcı indeksi: ${recipientIndex}`);

        // Yeniden deneme sayısını kontrol et
        if (retryCount >= MAX_RETRY_COUNT && recipient.status === 'RETRY') {
          console.log(`Maksimum yeniden deneme sayısına ulaşıldı (${MAX_RETRY_COUNT}). Kullanıcı: ${username}, Alıcı indeksi: ${recipientIndex}`);

          // Kullanıcı durumunu doğrudan güncelle
          updatedNotification.recipients[recipientIndex].status = 'FAILED';
          updatedNotification.recipients[recipientIndex].errorMessage = `Maksimum yeniden deneme sayısına ulaşıldı (${MAX_RETRY_COUNT}). Son hata: ${recipient.errorMessage || 'Bilinmeyen hata'}`;
          updatedNotification.markModified('recipients');
          await updatedNotification.save();

          console.log(`Kullanıcı durumu güncellendi: ${username}, Durum: FAILED (maksimum deneme)`);
          continue;
        }

        if (!telegramId) {
          console.log(`Kullanıcının Telegram ID'si yok: ${username}, Alıcı indeksi: ${recipientIndex}`);

          // Kullanıcı durumunu doğrudan güncelle
          updatedNotification.recipients[recipientIndex].status = 'FAILED';
          updatedNotification.recipients[recipientIndex].errorMessage = 'Telegram ID bulunamadı';
          updatedNotification.markModified('recipients');
          await updatedNotification.save();

          console.log(`Kullanıcı durumu güncellendi: ${username}, Durum: FAILED (Telegram ID yok)`);
          continue;
        }

        // Yeniden deneme durumunda log
        if (recipient.status === 'RETRY') {
          console.log(`Bildirim yeniden gönderiliyor. Kullanıcı: ${username}, Alıcı indeksi: ${recipientIndex}, Deneme: ${retryCount + 1}/${MAX_RETRY_COUNT}`);
        }

        // Butonları hazırla
        const inlineKeyboard = [];
        if (updatedNotification.buttons && updatedNotification.buttons.length > 0) {
          // Her satırda maksimum 1 buton olacak şekilde düzenle
          for (let i = 0; i < updatedNotification.buttons.length; i++) {
            const row = [];

            // Her buton için ayrı bir satır oluştur
            row.push({
              text: updatedNotification.buttons[i].text,
              url: updatedNotification.buttons[i].url
            });

            inlineKeyboard.push(row);
          }
        }

        const options = {
          parse_mode: 'Markdown',
          ...(inlineKeyboard.length > 0 && {
            reply_markup: {
              inline_keyboard: inlineKeyboard
            }
          })
        };

        // Fotoğraf varsa fotoğrafla gönder
        let sendSuccess = false;

        try {
          if (updatedNotification.image) {
            try {
              // Tam dosya yolunu oluştur
              const imagePath = path.join(process.cwd(), updatedNotification.image);
              console.info("Resim dosya yolu:", imagePath);

              // Dosyanın varlığını kontrol et
              if (fs.existsSync(imagePath)) {
                console.info("Fotoğraf bulundu, gönderiliyor...");
                await bot.sendPhoto(telegramId, imagePath, {
                  caption: message,
                  ...options
                });
                sendSuccess = true;
              } else {
                // Alternatif yol dene (başında / olmadan)
                const alternativePath = path.join(process.cwd(), updatedNotification.image.replace(/^\//, ''));
                console.info("Alternatif resim dosya yolu deneniyor:", alternativePath);

                if (fs.existsSync(alternativePath)) {
                  console.info("Fotoğraf alternatif yolda bulundu, gönderiliyor...");
                  await bot.sendPhoto(telegramId, alternativePath, {
                    caption: message,
                    ...options
                  });
                  sendSuccess = true;
                } else {
                  console.info("Fotoğraf bulunamadı, mesaj gönderiliyor...");
                  // Fotoğraf bulunamazsa sadece mesaj gönder
                  await bot.sendMessage(telegramId, message, options);
                  sendSuccess = true;
                }
              }
            } catch (photoError) {
              console.error("Fotoğraf gönderme hatası:", photoError);
              // Hata durumunda sadece mesaj gönder
              await bot.sendMessage(telegramId, message, options);
              sendSuccess = true;
            }
          } else {
            // Fotoğraf yoksa sadece mesaj gönder
            await bot.sendMessage(telegramId, message, options);
            sendSuccess = true;
          }

          // Başarılı gönderim durumunda kullanıcı durumunu güncelle
          if (sendSuccess) {
            console.log(`Bildirim başarıyla gönderildi. Kullanıcı: ${username}, ID: ${userIdStr}, Alıcı indeksi: ${recipientIndex}`);

            // Kullanıcı durumunu doğrudan güncelle
            updatedNotification.recipients[recipientIndex].status = 'SENT';
            updatedNotification.recipients[recipientIndex].sentAt = new Date();
            updatedNotification.recipients[recipientIndex].retryCount = 0;
            updatedNotification.markModified('recipients');
            await updatedNotification.save();

            console.log(`Kullanıcı durumu güncellendi: ${username}, Durum: SENT`);
          }
        } catch (sendError) {
          console.error(`Mesaj gönderme hatası: ${sendError.message}`);
          throw sendError; // Hata durumunda üst catch bloğuna ilet
        }
      } catch (error) {
        console.error(`Kullanıcıya bildirim gönderme hatası: ${error.message}`);
        let errorMessage = error.message;

        if (error.code === 403) {
          errorMessage = 'Kullanıcı botu engellemiş';
        } else if (error.code === 400) {
          errorMessage = 'Geçersiz mesaj formatı veya kullanıcı bulunamadı';
        } else if (error.code === 429) {
          errorMessage = 'Çok fazla istek gönderildi, limit aşıldı';
        }

        // Kullanıcı ID'sini güvenli bir şekilde al
        const userId = recipient.user && recipient.user._id ? recipient.user._id : recipient.user;
        if (!userId) {
          console.error('Geçersiz kullanıcı ID (hata durumunda):', recipient);
          continue;
        }

        // Kullanıcı ID'sini string'e çevir
        const userIdStr = String(userId);

        const username = recipient.user.username || 'Bilinmeyen Kullanıcı';

        // İlk gönderimde yeniden deneme mekanizmasını kaldırdık
        // Direkt olarak FAILED durumuna geçir
        console.log(`Bildirim gönderimi başarısız. Kullanıcı: ${username}, ID: ${userIdStr}, Alıcı indeksi: ${recipientIndex}`);

        // Kullanıcı durumunu doğrudan güncelle
        updatedNotification.recipients[recipientIndex].status = 'FAILED';
        updatedNotification.recipients[recipientIndex].errorMessage = `Hata: ${errorMessage}`;
        updatedNotification.recipients[recipientIndex].retryCount = updatedNotification.recipients[recipientIndex].retryCount + 1;
        updatedNotification.recipients[recipientIndex].lastRetryAt = new Date();
        updatedNotification.markModified('recipients');
        await updatedNotification.save();

        console.log(`Kullanıcı durumu güncellendi: ${username}, Durum: FAILED`);

        // Bildirim durumunu kontrol et
        // Eğer tüm alıcılar FAILED ise, bildirim durumunu FAILED olarak güncelle
        let allFailed = true;
        for (const recipient of updatedNotification.recipients) {
          if (recipient.status !== 'FAILED') {
            allFailed = false;
            break;
          }
        }

        if (allFailed) {
          updatedNotification.status = 'FAILED';
          await updatedNotification.save();
          console.log(`Tüm alıcılar başarısız olduğu için bildirim durumu FAILED olarak güncellendi`);
        }
      }
    }

    // Bildirim durumunu güncelle
    updatedNotification.currentIndex = endIndex;
    updatedNotification.lastProcessedAt = new Date();
    updatedNotification.retryCount = updatedNotification.retryCount + 1;
    updatedNotification.lastRetryAt = new Date();
    await updatedNotification.save();

    // Taze bildirim verisi al ve istatistikleri güncelle
    const updatedNotificationWithStats = await Notification.findById(updatedNotification._id);

    // İstatistikleri manuel olarak hesapla ve güncelle
    let sentCount = 0;
    let pendingCount = 0;
    let failedCount = 0;
    let retryCount = 0;

    updatedNotificationWithStats.recipients.forEach(recipient => {
      if (recipient.status === 'SENT') {
        sentCount++;
      } else if (recipient.status === 'FAILED') {
        failedCount++;
      } else if (recipient.status === 'RETRY') {
        retryCount++;
      } else if (recipient.status === 'PENDING') {
        pendingCount++;
      }
    });

    // İstatistikleri güncelle
    updatedNotificationWithStats.successCount = sentCount;
    updatedNotificationWithStats.failedCount = failedCount;
    updatedNotificationWithStats.pendingCount = pendingCount + retryCount;
    await updatedNotificationWithStats.save();

    console.log(`Bildirim durumu güncellendi. Toplam: ${updatedNotificationWithStats.totalRecipients}, Başarılı: ${sentCount}, Başarısız: ${failedCount}, Bekleyen: ${pendingCount + retryCount}`);

    // Tüm alıcılara gönderildi mi kontrol et
    if (endIndex >= updatedNotification.recipients.length) {
      await updatedNotification.updateStatus('COMPLETED');
      console.log('Bildirim gönderimi tamamlandı');
      return;
    }

    // Sonraki toplu gönderim için zamanlayıcı ayarla
    setTimeout(() => {
      processBatch(updatedNotification, bot, endIndex, batchSize, delayBetweenBatches);
    }, delayBetweenBatches);
  } catch (error) {
    console.error('Toplu bildirim işleme hatası:', error);
    try {
      // Hata durumunda bildirim ID'sini kullanarak bildirim nesnesini bul ve durumunu güncelle
      const notificationId = notification && notification._id ? notification._id : null;
      if (notificationId) {
        const errorNotification = await Notification.findById(notificationId);
        if (errorNotification) {
          await errorNotification.updateStatus('FAILED');
        }
      }
    } catch (innerError) {
      console.error('Hata durumunda bildirim güncelleme hatası:', innerError);
    }
  }
};

/**
 * Bildirim gönderimini duraklat
 */
const pauseNotification = async (notificationId) => {
  try {
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    if (notification.status !== 'SENDING') {
      throw new Error('Sadece gönderim durumundaki bildirimler duraklatılabilir');
    }

    await notification.updateStatus('PAUSED');
    return notification;
  } catch (error) {
    console.error('Bildirim duraklatma hatası:', error);
    throw error;
  }
};

/**
 * Bildirim gönderimini devam ettir
 */
const resumeNotification = async (notificationId) => {
  try {
    // Bildirim verisini ve kullanıcı bilgilerini al
    const notification = await Notification.findById(notificationId)
      .populate('recipients.user', 'telegramId username');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    if (notification.status !== 'PAUSED') {
      throw new Error('Sadece duraklatılmış bildirimler devam ettirilebilir');
    }

    // Bildirim durumunu güncelle
    await notification.updateStatus('SENDING');

    // Bot instance'ını al
    const bot = botService.getBot();
    if (!bot) {
      throw new Error('Bot başlatılamadı');
    }

    // Ayarları al
    const settings = await BotSettings.getSettings();
    const batchSize = settings.notifications.batchSize || 10; // Varsayılan değer
    const delayBetweenBatches = settings.notifications.delayBetweenBatches || 3000; // Varsayılan değer

    console.log(`Bildirim gönderimi devam ettiriliyor. Kalan alıcı: ${notification.totalRecipients - notification.currentIndex}, Batch size: ${batchSize}, Delay: ${delayBetweenBatches}ms`);

    // Gönderim işlemini kaldığı yerden devam ettir
    processBatch(notification, bot, notification.currentIndex, batchSize, delayBetweenBatches);

    return notification;
  } catch (error) {
    console.error('Bildirim devam ettirme hatası:', error);

    // Hata durumunda bildirim durumunu güncelle
    try {
      const notification = await Notification.findById(notificationId);
      if (notification) {
        await notification.updateStatus('FAILED');
      }
    } catch (innerError) {
      console.error('Hata durumunda bildirim güncelleme hatası:', innerError);
    }

    throw error;
  }
};

/**
 * Bildirim gönderimini durdur
 */
const stopNotification = async (notificationId) => {
  try {
    const notification = await Notification.findById(notificationId);
    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    if (notification.status !== 'SENDING' && notification.status !== 'PAUSED') {
      throw new Error('Sadece gönderim durumundaki veya duraklatılmış bildirimler durdurulabilir');
    }

    // Kalan alıcıları başarısız olarak işaretle
    for (let i = 0; i < notification.recipients.length; i++) {
      const recipient = notification.recipients[i];
      if (recipient.status === 'PENDING' || recipient.status === 'RETRY') {
        console.log(`Bildirim durduruldu. Alıcı #${i} durumu FAILED olarak güncelleniyor.`);

        // Kullanıcı durumunu doğrudan güncelle
        notification.recipients[i].status = 'FAILED';
        notification.recipients[i].errorMessage = 'Bildirim gönderimi durduruldu';
        notification.markModified('recipients');
      }
    }

    // Değişiklikleri kaydet
    await notification.save();

    // İstatistikleri güncelle
    let sentCount = 0;
    let pendingCount = 0;
    let failedCount = 0;
    let retryCount = 0;

    notification.recipients.forEach(recipient => {
      if (recipient.status === 'SENT') {
        sentCount++;
      } else if (recipient.status === 'FAILED') {
        failedCount++;
      } else if (recipient.status === 'RETRY') {
        retryCount++;
      } else if (recipient.status === 'PENDING') {
        pendingCount++;
      }
    });

    notification.successCount = sentCount;
    notification.failedCount = failedCount;
    notification.pendingCount = pendingCount + retryCount;
    await notification.save();

    await notification.updateStatus('COMPLETED');
    return notification;
  } catch (error) {
    console.error('Bildirim durdurma hatası:', error);
    throw error;
  }
};

/**
 * Bildirim detaylarını getir
 */
const getNotificationDetails = async (notificationId) => {
  try {
    const notification = await Notification.findById(notificationId)
      .populate('createdBy', 'name username')
      .populate('recipients.user', 'username telegramId');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    return notification;
  } catch (error) {
    console.error('Bildirim detayları getirme hatası:', error);
    throw error;
  }
};

/**
 * Bildirimleri listele
 */
const listNotifications = async (filters = {}, page = 1, limit = 10) => {
  try {
    const query = {};

    if (filters.status) {
      query.status = filters.status;
    }

    if (filters.createdBy) {
      query.createdBy = filters.createdBy;
    }

    const skip = (page - 1) * limit;

    const notifications = await Notification.find(query)
      .populate('createdBy', 'name username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Notification.countDocuments(query);

    return {
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Bildirimleri listeleme hatası:', error);
    throw error;
  }
};

/**
 * Günlük bildirim limitini kontrol et
 */
const checkDailyLimit = async () => {
  try {
    const settings = await BotSettings.getSettings();
    const dailyLimit = settings.notifications.dailyLimit;

    // Bugünün başlangıcı
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Bugün gönderilen bildirim sayısı
    const sentToday = await Notification.countDocuments({
      startedAt: { $gte: today },
      status: { $in: ['SENDING', 'PAUSED', 'COMPLETED'] }
    });

    return {
      limit: dailyLimit,
      sent: sentToday,
      remaining: Math.max(0, dailyLimit - sentToday),
      canSend: sentToday < dailyLimit
    };
  } catch (error) {
    console.error('Günlük limit kontrol hatası:', error);
    throw error;
  }
};

/**
 * Tek bir alıcıya bildirimi yeniden gönder
 */
const retrySingleRecipient = async (notificationId, userId) => {
  try {
    // Bildirim verisini ve kullanıcı bilgilerini al
    const notification = await Notification.findById(notificationId)
      .populate('recipients.user', 'telegramId username');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Sadece tamamlanmış veya başarısız bildirimleri yeniden gönderebiliriz
    if (notification.status !== 'COMPLETED' && notification.status !== 'FAILED') {
      throw new Error('Sadece tamamlanmış veya başarısız bildirimler yeniden gönderilebilir');
    }

    // Kullanıcıyı bul
    const userIdStr = String(userId);
    let recipientIndex = -1;

    // Kullanıcıyı bul
    for (let i = 0; i < notification.recipients.length; i++) {
      const r = notification.recipients[i];
      if (!r.user) continue;

      let recipientUserId = '';

      if (typeof r.user === 'object' && r.user.$oid) {
        recipientUserId = r.user.$oid;
      } else if (typeof r.user === 'object' && r.user._id) {
        recipientUserId = String(r.user._id);
      } else {
        recipientUserId = String(r.user);
      }

      if (recipientUserId === userIdStr) {
        recipientIndex = i;
        break;
      }
    }

    if (recipientIndex === -1) {
      throw new Error('Belirtilen kullanıcı bu bildirimde bulunamadı');
    }

    // Mevcut deneme sayısını kontrol et
    const currentRetryCount = notification.recipients[recipientIndex].retryCount || 0;

    // Maksimum deneme sayısını kontrol et (sabit değer)
    const MAX_RETRY_COUNT = 3;

    if (currentRetryCount >= MAX_RETRY_COUNT) {
      throw new Error(`Bu alıcı için maksimum deneme sayısına ulaşıldı (${currentRetryCount}/${MAX_RETRY_COUNT}). Daha fazla deneme yapılamaz.`);
    }

    // Kullanıcı durumunu PENDING olarak güncelle
    notification.recipients[recipientIndex].status = 'PENDING';
    // Deneme sayısını sıfırlama, mevcut değeri koru
    notification.recipients[recipientIndex].errorMessage = null;
    notification.markModified('recipients');

    // İstatistikleri güncelle
    let sentCount = 0;
    let pendingCount = 0;
    let failedCount = 0;
    let retryCount = 0;

    notification.recipients.forEach(recipient => {
      if (recipient.status === 'SENT') {
        sentCount++;
      } else if (recipient.status === 'FAILED') {
        failedCount++;
      } else if (recipient.status === 'RETRY') {
        retryCount++;
      } else if (recipient.status === 'PENDING') {
        pendingCount++;
      }
    });

    notification.successCount = sentCount;
    notification.failedCount = failedCount;
    notification.pendingCount = pendingCount + retryCount;

    // Bildirim durumunu güncelle - Tek alıcı için SENDING durumuna geçmeye gerek yok
    // Mevcut durumu koruyalım (COMPLETED veya FAILED)

    // Bot instance'ını al
    const bot = botService.getBot();
    if (!bot) {
      throw new Error('Bot başlatılamadı');
    }

    // Tek bir alıcıya gönder
    const recipient = notification.recipients[recipientIndex];
    const telegramId = recipient.user.telegramId;
    const username = recipient.user.username || 'Bilinmeyen Kullanıcı';

    if (!telegramId) {
      throw new Error(`Kullanıcının Telegram ID'si yok: ${username}`);
    }

    console.log(`Tek alıcıya bildirim yeniden gönderiliyor. Kullanıcı: ${username}, Telegram ID: ${telegramId}`);

    // Mesajı hazırla
    let message = notification.message;

    // Butonları ekle
    const buttons = notification.buttons || [];
    const options = {};

    if (buttons.length > 0) {
      const inlineKeyboard = [];
      // Her satırda maksimum 1 buton olacak şekilde düzenle
      for (let i = 0; i < buttons.length; i++) {
        const row = [{
          text: buttons[i].text,
          url: buttons[i].url
        }];
        inlineKeyboard.push(row);
      }

      options.reply_markup = {
        inline_keyboard: inlineKeyboard
      };
    }

    // Fotoğraf varsa fotoğrafla gönder
    let sendSuccess = false;

    try {
      if (notification.image) {
        try {
          // Tam dosya yolunu oluştur
          const imagePath = path.join(process.cwd(), notification.image);
          console.info("Resim dosya yolu:", imagePath);

          // Dosyanın varlığını kontrol et
          if (fs.existsSync(imagePath)) {
            console.info("Fotoğraf bulundu, gönderiliyor...");
            await bot.sendPhoto(telegramId, imagePath, {
              caption: message,
              ...options
            });
            sendSuccess = true;
          } else {
            // Alternatif yol dene (başında / olmadan)
            const alternativePath = path.join(process.cwd(), notification.image.replace(/^\//, ''));
            console.info("Alternatif resim dosya yolu deneniyor:", alternativePath);

            if (fs.existsSync(alternativePath)) {
              console.info("Fotoğraf alternatif yolda bulundu, gönderiliyor...");
              await bot.sendPhoto(telegramId, alternativePath, {
                caption: message,
                ...options
              });
              sendSuccess = true;
            } else {
              console.info("Fotoğraf bulunamadı, mesaj gönderiliyor...");
              // Fotoğraf bulunamazsa sadece mesaj gönder
              await bot.sendMessage(telegramId, message, options);
              sendSuccess = true;
            }
          }
        } catch (photoError) {
          console.error("Fotoğraf gönderme hatası:", photoError);
          // Hata durumunda sadece mesaj gönder
          await bot.sendMessage(telegramId, message, options);
          sendSuccess = true;
        }
      } else {
        // Fotoğraf yoksa sadece mesaj gönder
        await bot.sendMessage(telegramId, message, options);
        sendSuccess = true;
      }

      // Başarılı gönderim durumunda kullanıcı durumunu güncelle
      if (sendSuccess) {
        console.log(`Bildirim başarıyla gönderildi. Kullanıcı: ${username}, ID: ${userIdStr}`);

        // Kullanıcı durumunu doğrudan güncelle
        notification.recipients[recipientIndex].status = 'SENT';
        notification.recipients[recipientIndex].sentAt = new Date();
        notification.recipients[recipientIndex].retryCount = 0;
        notification.markModified('recipients');
        await notification.save();

        console.log(`Kullanıcı durumu güncellendi: ${username}, Durum: SENT`);
      }
    } catch (error) {
      console.error(`Tek alıcıya bildirim gönderme hatası: ${error.message}`);

      // Hata durumunda kullanıcı durumunu güncelle
      // Tekli yeniden göndermede yeniden deneme mekanizmasını kaldırdık
      // Direkt olarak FAILED durumuna geçir
      notification.recipients[recipientIndex].status = 'FAILED';
      notification.recipients[recipientIndex].errorMessage = `Hata: ${error.message}`;
      notification.recipients[recipientIndex].retryCount = notification.recipients[recipientIndex].retryCount + 1;

      notification.markModified('recipients');

      // İstatistikleri güncelle
      let sentCount = 0;
      let pendingCount = 0;
      let failedCount = 0;

      notification.recipients.forEach(recipient => {
        if (recipient.status === 'SENT') {
          sentCount++;
        } else if (recipient.status === 'FAILED') {
          failedCount++;
        } else if (recipient.status === 'PENDING') {
          pendingCount++;
        }
      });

      notification.successCount = sentCount;
      notification.failedCount = failedCount;
      notification.pendingCount = pendingCount;

      await notification.save();

      console.log(`Tek alıcıya bildirim gönderme başarısız. Kullanıcı: ${username}, Hata: ${error.message}`);
      throw error;
    }

    return notification;
  } catch (error) {
    console.error('Tek alıcıya bildirim gönderme hatası:', error);
    throw error;
  }
};

/**
 * Başarısız bildirimi yeniden gönder (FAILED durumundaki bildirimler için)
 */
const resendNotification = async (notificationId) => {
  try {
    // Bildirim verisini ve kullanıcı bilgilerini al
    const notification = await Notification.findById(notificationId)
      .populate('recipients.user', 'telegramId username');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Sadece FAILED durumundaki bildirimleri yeniden gönderebiliriz
    if (notification.status !== 'FAILED') {
      throw new Error('Sadece başarısız bildirimler yeniden gönderilebilir');
    }

    // Bildirim yeniden deneme sayısını kontrol et
    const notificationRetryCount = notification.retryCount || 0;
    const MAX_NOTIFICATION_RETRY = 3; // Bildirim için maksimum yeniden deneme sayısı

    if (notificationRetryCount >= MAX_NOTIFICATION_RETRY) {
      throw new Error(`Bu bildirim için maksimum yeniden gönderme sayısına ulaşıldı (${notificationRetryCount}/${MAX_NOTIFICATION_RETRY}). Daha fazla deneme yapılamaz.`);
    }

    // Bildirim yeniden deneme sayısını artır
    notification.retryCount = notificationRetryCount + 1;

    console.info(`Bildirim yeniden gönderiliyor. Deneme: ${notification.retryCount}/${MAX_NOTIFICATION_RETRY}`);

    // Tüm alıcıları PENDING durumuna getir
    // Ancak her alıcının kendi retryCount değerini sıfırlama, böylece toplam deneme sayısı takip edilebilir
    for (let i = 0; i < notification.recipients.length; i++) {
      // Sadece FAILED durumundaki alıcıları PENDING durumuna getir
      if (notification.recipients[i].status === 'FAILED') {
        notification.recipients[i].status = 'PENDING';
        notification.recipients[i].errorMessage = null;
      }
    }

    notification.markModified('recipients');

    // İstatistikleri güncelle
    let sentCount = 0;
    let pendingCount = 0;
    let failedCount = 0;

    notification.recipients.forEach(recipient => {
      if (recipient.status === 'SENT') {
        sentCount++;
      } else if (recipient.status === 'FAILED') {
        failedCount++;
      } else if (recipient.status === 'PENDING') {
        pendingCount++;
      }
    });

    notification.successCount = sentCount;
    notification.failedCount = failedCount;
    notification.pendingCount = pendingCount;

    // Bildirim durumunu güncelle
    notification.status = 'SENDING';
    notification.currentIndex = 0; // Baştan başla
    notification.lastProcessedAt = new Date();
    notification.lastRetryAt = new Date(); // Son yeniden deneme zamanını kaydet
    await notification.save();

    // Bot instance'ını al
    const bot = botService.getBot();
    if (!bot) {
      throw new Error('Bot başlatılamadı');
    }

    // Ayarları al
    const settings = await BotSettings.getSettings();
    const batchSize = settings.notifications.batchSize || 10; // Varsayılan değer
    const delayBetweenBatches = settings.notifications.delayBetweenBatches || 3000; // Varsayılan değer

    console.log(`Bildirim yeniden gönderiliyor. Deneme: ${notification.retryCount}/${MAX_NOTIFICATION_RETRY}, Toplam alıcı: ${notification.recipients.length}, Batch size: ${batchSize}, Delay: ${delayBetweenBatches}ms`);

    // Gönderim işlemini başlat
    processBatch(notification, bot, 0, batchSize, delayBetweenBatches);

    return notification;
  } catch (error) {
    console.error('Bildirimi yeniden gönderme hatası:', error);
    throw error;
  }
};

/**
 * Başarısız bildirimleri yeniden gönder
 */
const retryFailedNotification = async (notificationId) => {
  try {
    // Bildirim verisini ve kullanıcı bilgilerini al
    const notification = await Notification.findById(notificationId)
      .populate('recipients.user', 'telegramId username');

    if (!notification) {
      throw new Error('Bildirim bulunamadı');
    }

    // Sadece tamamlanmış veya başarısız bildirimleri yeniden gönderebiliriz
    if (notification.status !== 'COMPLETED' && notification.status !== 'FAILED') {
      throw new Error('Sadece tamamlanmış veya başarısız bildirimler yeniden gönderilebilir');
    }

    // Başarısız alıcıları kontrol et
    const failedRecipients = notification.recipients.filter(r => r.status === 'FAILED');

    if (failedRecipients.length === 0) {
      throw new Error('Yeniden gönderilecek başarısız alıcı bulunamadı');
    }

    console.log(`${failedRecipients.length} başarısız alıcı bulundu. Yeniden gönderim başlatılıyor...`);

    // Başarısız alıcıları PENDING durumuna getir
    for (let i = 0; i < notification.recipients.length; i++) {
      if (notification.recipients[i].status === 'FAILED') {
        notification.recipients[i].status = 'PENDING';
        notification.recipients[i].errorMessage = null;
      }
    }

    notification.markModified('recipients');

    // İstatistikleri güncelle
    let sentCount = 0;
    let pendingCount = 0;
    let failedCount = 0;

    notification.recipients.forEach(recipient => {
      if (recipient.status === 'SENT') {
        sentCount++;
      } else if (recipient.status === 'FAILED') {
        failedCount++;
      } else if (recipient.status === 'PENDING') {
        pendingCount++;
      }
    });

    notification.successCount = sentCount;
    notification.failedCount = failedCount;
    notification.pendingCount = pendingCount;

    // Bildirim durumunu güncelle
    notification.status = 'SENDING';
    notification.currentIndex = 0; // Baştan başla
    notification.lastProcessedAt = new Date();
    await notification.save();

    // Bot instance'ını al
    const bot = botService.getBot();
    if (!bot) {
      throw new Error('Bot başlatılamadı');
    }

    // Ayarları al
    const settings = await BotSettings.getSettings();
    const batchSize = settings.notifications.batchSize || 10; // Varsayılan değer
    const delayBetweenBatches = settings.notifications.delayBetweenBatches || 3000; // Varsayılan değer

    console.log(`Başarısız bildirimler yeniden gönderiliyor. Toplam: ${failedRecipients.length}, Batch size: ${batchSize}, Delay: ${delayBetweenBatches}ms`);

    // Gönderim işlemini başlat
    processBatch(notification, bot, 0, batchSize, delayBetweenBatches);

    return notification;
  } catch (error) {
    console.error('Başarısız bildirimleri yeniden gönderme hatası:', error);
    throw error;
  }
};

module.exports = {
  createNotification,
  previewNotification,
  sendNotification,
  pauseNotification,
  resumeNotification,
  stopNotification,
  getNotificationDetails,
  listNotifications,
  checkDailyLimit,
  retryFailedNotification,
  retrySingleRecipient,
  resendNotification
};
