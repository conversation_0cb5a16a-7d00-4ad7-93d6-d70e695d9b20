const mongoose = require('mongoose');
const crypto = require('crypto');

const ShortUrlSchema = new mongoose.Schema(
  {
    originalUrl: {
      type: String,
      required: true
    },
    shortCode: {
      type: String,
      required: true,
      unique: true
    },
    notification: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Notification'
    },
    urlIndex: {
      type: Number
    },
    clicks: {
      type: Number,
      default: 0
    },
    clickedBy: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      clickedAt: {
        type: Date,
        default: Date.now
      }
    }],
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 60 * 60 * 24 * 30 // 30 gün sonra otomatik silme
    }
  }
);

// Kısa kod oluşturma
ShortUrlSchema.statics.generateShortCode = function() {
  return crypto.randomBytes(4).toString('hex');
};

// URL oluşturma
ShortUrlSchema.statics.createShortUrl = async function(originalUrl, notification = null, urlIndex = null) {
  let shortCode = this.generateShortCode();
  
  // Benzersiz kısa kod oluşturma
  while (await this.findOne({ shortCode })) {
    shortCode = this.generateShortCode();
  }
  
  const shortUrl = await this.create({
    originalUrl,
    shortCode,
    notification,
    urlIndex
  });
  
  return shortUrl;
};

// Tıklama kaydetme
ShortUrlSchema.methods.recordClick = async function(userId) {
  this.clicks++;
  
  const userClick = this.clickedBy.find(
    click => click.user.toString() === userId.toString()
  );
  
  if (userClick) {
    userClick.clickedAt = new Date();
  } else {
    this.clickedBy.push({
      user: userId,
      clickedAt: new Date()
    });
  }
  
  await this.save();
  
  // Bildirim varsa, bildirimde de tıklamayı kaydet
  if (this.notification && this.urlIndex !== null) {
    const Notification = mongoose.model('Notification');
    const notification = await Notification.findById(this.notification);
    
    if (notification) {
      await notification.recordUrlClick(this.urlIndex, userId);
    }
  }
  
  return this;
};

module.exports = mongoose.model('ShortUrl', ShortUrlSchema);
