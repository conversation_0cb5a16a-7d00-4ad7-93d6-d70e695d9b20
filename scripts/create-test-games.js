const mongoose = require('mongoose');
const Contest = require('../src/models/Contest');
const Admin = require('../src/models/Admin');
require('dotenv').config();

// MongoDB bağlantısı
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/telegram-bot-loto');
    console.log('✅ MongoDB bağlantısı başarılı');
  } catch (error) {
    console.error('❌ MongoDB bağlantı hatası:', error);
    process.exit(1);
  }
};

// Test oyunları oluştur
const createTestGames = async () => {
  try {
    console.log('🎮 Test emoji oyunları oluşturuluyor...\n');

    // Admin kullanıcısı bul veya oluştur
    let admin = await Admin.findOne();
    if (!admin) {
      admin = await Admin.create({
        username: 'test-admin',
        email: '<EMAIL>',
        password: 'test123', // Bu hash'lenecek
        isActive: true
      });
      console.log('👤 Test admin kullanıcısı oluşturuldu');
    }

    // Mevcut test oyunlarını sil
    await Contest.deleteMany({
      title: { $regex: /^TEST -/ }
    });
    console.log('🗑️  Eski test oyunları temizlendi\n');

    const now = new Date();
    const startDate = new Date(now);
    const endDate = new Date(now);
    endDate.setHours(endDate.getHours() + 24); // 24 saat sonra

    // Ortak contest özellikleri
    const baseContestData = {
      type: 'EMOJI_GAME',
      status: 'ACTIVE',
      startDate: startDate,
      endDate: endDate,
      createdBy: admin._id,
      channelRequirement: {
        required: false,
        channelUsername: ''
      }
    };

    // 1. ZAR OYUNU - Toplam Değer
    const diceGameTotal = await Contest.create({
      ...baseContestData,
      title: 'TEST - 🎲 Zar Oyunu (Toplam 30)',
      description: '10 zar atışı yapın ve toplam 30 puana ulaşmaya çalışın!',
      requirements: 'Toplam 30 puan hedefi',
      prizes: '🏆 Test ödülü',
      emojiGame: {
        gameType: 'DICE',
        attemptCount: 10,
        diceSettings: {
          targetType: 'TOTAL_VALUE',
          targetValue: 30
        }
      }
    });

    // Test oyunları listesi
    const testGames = [
      {
        title: 'TEST - 🎲 Zar Oyunu (3 Altılı)',
        description: '10 zar atışı yapın ve en az 3 kez 6 gelsin!',
        requirements: 'En az 3 kez 6 gelsin',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'DICE',
          attemptCount: 10,
          diceSettings: {
            targetType: 'SPECIFIC_VALUE_COUNT',
            targetValue: 6,
            targetCount: 3
          }
        }
      },
      {
        title: 'TEST - 🏀 Basketbol Oyunu',
        description: '5 atış yapın ve en az 2 basket atın!',
        requirements: 'En az 2 başarılı atış',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'BASKETBALL',
          attemptCount: 5,
          successTarget: 2
        }
      },
      {
        title: 'TEST - ⚽ Futbol Oyunu',
        description: '5 penaltı atın ve en az 3 gol atın!',
        requirements: 'En az 3 gol',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'FOOTBALL',
          attemptCount: 5,
          successTarget: 3
        }
      },
      {
        title: 'TEST - 🎯 Dart Oyunu',
        description: '8 dart atın ve en az 1 bullseye vurun!',
        requirements: 'En az 1 bullseye',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'DART',
          attemptCount: 8,
          bullseyeTarget: 1
        }
      },
      {
        title: 'TEST - 🎳 Bowling Oyunu',
        description: '6 top atın ve en az 1 strike yapın!',
        requirements: 'En az 1 strike',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'BOWLING',
          attemptCount: 6,
          strikeTarget: 1
        }
      },
      {
        title: 'TEST - 🎰 Slot Oyunu (Tüm)',
        description: '5 çevirme yapın ve herhangi bir kazanan kombinasyon yakalayın!',
        requirements: 'Herhangi bir kazanan kombinasyon',
        prizes: '🏆 Test ödülü',
        emojiGame: {
          gameType: 'SLOT',
          attemptCount: 5,
          slotSettings: {
            winningCombinations: [
              { combination: ['🍒', '🍒', '🍒'], name: 'Üç Kiraz' },
              { combination: ['🍇', '🍇', '🍇'], name: 'Üç Üzüm' },
              { combination: ['🍋', '🍋', '🍋'], name: 'Üç Limon' },
              { combination: ['7️⃣', '7️⃣', '7️⃣'], name: 'Üç Yedi' }
            ]
          }
        }
      },
      {
        title: 'TEST - 🎰 Slot Oyunu (Jackpot)',
        description: '3 çevirme yapın ve 7️⃣7️⃣7️⃣ jackpot yakalayın!',
        requirements: 'Sadece 7️⃣7️⃣7️⃣ jackpot',
        prizes: '💰 Jackpot ödülü',
        emojiGame: {
          gameType: 'SLOT',
          attemptCount: 3,
          slotSettings: {
            winningCombinations: [
              { combination: ['7️⃣', '7️⃣', '7️⃣'], name: 'Jackpot' }
            ]
          }
        }
      }
    ];

    // Tüm test oyunlarını oluştur
    const createdGames = [];
    for (const gameData of testGames) {
      const contest = await Contest.create({
        ...baseContestData,
        ...gameData
      });
      createdGames.push(contest);
    }

    console.log('✅ Test oyunları başarıyla oluşturuldu!\n');

    console.log('📋 Oluşturulan oyunlar:');
    console.log(`1. ${diceGameTotal.title}`);
    createdGames.forEach((game, index) => {
      console.log(`${index + 2}. ${game.title}`);
    });

    console.log('\n🎯 Test için botunuzda /contests komutunu kullanın!');

  } catch (error) {
    console.error('❌ Test oyunları oluşturulurken hata:', error);
  }
};

// Ana fonksiyon
const main = async () => {
  await connectDB();
  await createTestGames();
  await mongoose.connection.close();
  console.log('\n✅ İşlem tamamlandı, veritabanı bağlantısı kapatıldı');
  process.exit(0);
};

// Script çalıştır
main().catch(error => {
  console.error('❌ Script hatası:', error);
  process.exit(1);
});
