const ShortUrl = require('../models/ShortUrl');
const User = require('../models/User');

/**
 * Kısa URL'yi orijinal URL'ye yönlendir
 */
const redirectShortUrl = async (shortCode, userId) => {
  try {
    const shortUrl = await ShortUrl.findOne({ shortCode });
    
    if (!shortUrl) {
      throw new Error('Kısa URL bulunamadı');
    }
    
    // Tıklama kaydı
    if (userId) {
      await shortUrl.recordClick(userId);
    } else {
      shortUrl.clicks++;
      await shortUrl.save();
    }
    
    return shortUrl.originalUrl;
  } catch (error) {
    console.error('Kısa URL yönlendirme hatası:', error);
    throw error;
  }
};

/**
 * Kısa URL oluştur
 */
const createShortUrl = async (originalUrl, notificationId = null, urlIndex = null) => {
  try {
    const shortUrl = await ShortUrl.createShortUrl(originalUrl, notificationId, urlIndex);
    return shortUrl;
  } catch (error) {
    console.error('Kısa URL oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Kısa URL istatistiklerini getir
 */
const getShortUrlStats = async (shortCode) => {
  try {
    const shortUrl = await ShortUrl.findOne({ shortCode })
      .populate('clickedBy.user', 'username telegramId');
    
    if (!shortUrl) {
      throw new Error('Kısa URL bulunamadı');
    }
    
    return {
      originalUrl: shortUrl.originalUrl,
      shortCode: shortUrl.shortCode,
      clicks: shortUrl.clicks,
      clickedBy: shortUrl.clickedBy
    };
  } catch (error) {
    console.error('Kısa URL istatistikleri getirme hatası:', error);
    throw error;
  }
};

module.exports = {
  redirectShortUrl,
  createShortUrl,
  getShortUrlStats
};
