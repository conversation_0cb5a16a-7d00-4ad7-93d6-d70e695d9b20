const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Admin = require('../models/Admin');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Update admin permissions
const updateAdminPermissions = async () => {
  try {
    // Find admin user
    const admin = await Admin.findOne({ username: 'admin' });
    
    if (!admin) {
      console.log('Admin user not found');
      return;
    }
    
    // Update permissions
    admin.permissions = {
      sendNotifications: true,
      manageUsers: true,
      manageContests: true,
      manageAdmins: true,
      viewReports: true,
      manageBotSettings: true
    };
    
    // Save changes
    await admin.save();
    console.log('Admin permissions updated successfully');
  } catch (error) {
    console.error('Error updating admin permissions:', error);
  } finally {
    // Close connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
};

// Run the function
updateAdminPermissions();
