const mongoose = require('mongoose');

const SubmissionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    contest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Contest',
      required: true,
    },
    // For sports prediction contests
    answers: {
      type: Map,
      of: String,
      default: {},
    },
    correctAnswers: {
      type: Number,
      default: 0,
    },
    // For emoji game contests
    emojiResults: {
      gameType: String,
      attempts: [{
        attemptNumber: Number,
        result: mongoose.Schema.Types.Mixed, // Can be number, array, or object depending on game type
        success: {
          type: Boolean,
          default: false,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
      }],
      totalScore: Number,
      achievedTarget: Boolean,
    },
    isWinner: {
      type: Boolean,
      default: false,
    },
    submittedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Compound index to ensure a user can only submit once per contest
SubmissionSchema.index({ user: 1, contest: 1 }, { unique: true });

module.exports = mongoose.model('Submission', SubmissionSchema);
