const Contest = require('../models/Contest');
const Submission = require('../models/Submission');
const User = require('../models/User');
const Admin = require('../models/Admin');
const jwt = require('jsonwebtoken');
const ExcelJS = require('exceljs');
const bot = require('../bot/bot');
const { getTranslation } = require('../bot/translations');

// @desc    Create a new contest
// @route   POST /api/contests
// @access  Private/Admin
exports.createContest = async (req, res) => {
  try {
    const {
      title,
      description,
      startDate,
      endDate,
      type,
      questions,
      requirements,
      prizes,
      minCorrectAnswers,
      channelRequirement,
      emojiGame,
    } = req.body;

    // Validation
    const errors = [];

    // Check if end date is after start date
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (endDateObj <= startDateObj) {
      errors.push('<PERSON>i<PERSON> tarihi başlangıç tarihinden sonra olmalıdır');
    }

    // Validate based on contest type
    if (type === 'SPORTS_PREDICTION') {
      if (!questions || questions.length === 0) {
        errors.push('Spor tahmin yarışması için en az bir soru gereklidir');
      }
      if (!minCorrectAnswers || minCorrectAnswers < 1) {
        errors.push('Minimum doğru cevap sayısı en az 1 olmalıdır');
      }
    } else if (type === 'EMOJI_GAME') {
      if (!emojiGame || !emojiGame.gameType) {
        errors.push('Emoji oyunu için oyun tipi seçilmelidir');
      }
      if (!emojiGame.attemptCount || emojiGame.attemptCount < 1 || emojiGame.attemptCount > 50) {
        errors.push('Deneme sayısı 1-50 arasında olmalıdır');
      }

      // Validate game-specific settings
      if (emojiGame.gameType === 'DICE') {
        if (!emojiGame.diceSettings || !emojiGame.diceSettings.targetType) {
          errors.push('Zar oyunu için hedef tipi seçilmelidir');
        }
        if (emojiGame.diceSettings.targetType === 'TOTAL_VALUE' && !emojiGame.diceSettings.targetValue) {
          errors.push('Toplam değer hedefi için hedef değer girilmelidir');
        }
        if (emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT' &&
          (!emojiGame.diceSettings.targetValue || !emojiGame.diceSettings.targetCount)) {
          errors.push('Belirli değer sayısı hedefi için hedef değer ve sayı girilmelidir');
        }
      } else if (['BASKETBALL', 'FOOTBALL'].includes(emojiGame.gameType)) {
        if (!emojiGame.successTarget || emojiGame.successTarget < 1) {
          errors.push('Başarı hedefi en az 1 olmalıdır');
        }
      } else if (emojiGame.gameType === 'DART') {
        if (!emojiGame.bullseyeTarget || emojiGame.bullseyeTarget < 1) {
          errors.push('Bullseye hedefi en az 1 olmalıdır');
        }
      } else if (emojiGame.gameType === 'BOWLING') {
        if (!emojiGame.strikeTarget || emojiGame.strikeTarget < 1) {
          errors.push('Strike hedefi en az 1 olmalıdır');
        }
      } else if (emojiGame.gameType === 'SLOT') {
        if (!emojiGame.slotSettings || !emojiGame.slotSettings.winningCombinations ||
          emojiGame.slotSettings.winningCombinations.length === 0) {
          errors.push('Slot oyunu için en az bir kazanan kombinasyon seçilmelidir');
        }
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        message: errors.join(', '),
        errors,
      });
    }

    // Create contest data
    const contestData = {
      title,
      description,
      startDate,
      endDate,
      type: type || 'SPORTS_PREDICTION',
      requirements,
      prizes,
      channelRequirement,
      createdBy: req.admin.id,
      status: 'ACTIVE',
    };

    // Add type-specific data
    if (type === 'SPORTS_PREDICTION') {
      contestData.questions = questions;
      contestData.minCorrectAnswers = minCorrectAnswers;
    } else if (type === 'EMOJI_GAME') {
      contestData.emojiGame = emojiGame;
    }

    // Create contest
    const contest = await Contest.create(contestData);

    res.status(201).json({
      success: true,
      contest,
    });
  } catch (error) {
    console.error('Create contest error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get all contests
// @route   GET /api/contests
// @access  Private/Admin
exports.getContests = async (req, res) => {
  try {
    const contests = await Contest.find()
      .sort({ createdAt: -1 })
      .populate('createdBy', 'name username');

    // Get participant counts and winner counts for each contest
    const contestsWithCounts = await Promise.all(
      contests.map(async (contest) => {
        const participantCount = await Submission.countDocuments({ contest: contest._id });

        // Get winner count for completed contests
        let winnerCount = 0;
        if (contest.status === 'COMPLETED') {
          winnerCount = await Submission.countDocuments({
            contest: contest._id,
            isWinner: true
          });
        }

        const contestObj = contest.toObject();
        contestObj.participantCount = participantCount;
        contestObj.winnerCount = winnerCount;
        return contestObj;
      })
    );

    res.status(200).json({
      success: true,
      count: contests.length,
      contests: contestsWithCounts,
    });
  } catch (error) {
    console.error('Get contests error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single contest
// @route   GET /api/contests/:id
// @access  Private/Admin
exports.getContest = async (req, res) => {
  try {
    const contest = await Contest.findById(req.params.id)
      .populate('createdBy', 'name username')
      .populate('cancelledBy', 'name username');

    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    // Get participant count
    const participantCount = await Submission.countDocuments({ contest: contest._id });

    // Get winner count for completed contests
    let winnerCount = 0;
    if (contest.status === 'COMPLETED') {
      winnerCount = await Submission.countDocuments({
        contest: contest._id,
        isWinner: true
      });
    }

    // Convert to object to add the counts
    const contestObj = contest.toObject();
    contestObj.participantCount = participantCount;
    contestObj.winnerCount = winnerCount;

    res.status(200).json({
      success: true,
      contest: contestObj,
    });
  } catch (error) {
    console.error('Get contest error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update contest
// @route   PUT /api/contests/:id
// @access  Private/Admin
exports.updateContest = async (req, res) => {
  try {
    const {
      title,
      description,
      startDate,
      endDate,
      questions,
      requirements,
      prizes,
      minCorrectAnswers,
      channelRequirement,
      status,
      emojiGame,
    } = req.body;

    let contest = await Contest.findById(req.params.id);

    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    // Check if contest is already completed
    if (contest.status === 'COMPLETED' && status !== 'COMPLETED') {
      return res.status(400).json({
        message: 'Tamamlanmış bir yarışma düzenlenemez',
      });
    }

    // Prevent changing contest type if there are submissions
    const submissionCount = await Submission.countDocuments({ contest: req.params.id });
    if (submissionCount > 0 && contest.type !== req.body.type) {
      return res.status(400).json({
        message: 'Katılımcı bulunan yarışmanın tipi değiştirilemez',
      });
    }

    // Validation
    const errors = [];

    // Check if end date is after start date
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (endDateObj <= startDateObj) {
      errors.push('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
    }

    // Validate based on contest type
    if (contest.type === 'SPORTS_PREDICTION') {
      if (!questions || questions.length === 0) {
        errors.push('Spor tahmin yarışması için en az bir soru gereklidir');
      }
      if (!minCorrectAnswers || minCorrectAnswers < 1) {
        errors.push('Minimum doğru cevap sayısı en az 1 olmalıdır');
      }
    } else if (contest.type === 'EMOJI_GAME') {
      if (!emojiGame || !emojiGame.gameType) {
        errors.push('Emoji oyunu için oyun tipi seçilmelidir');
      }
      if (!emojiGame.attemptCount || emojiGame.attemptCount < 1 || emojiGame.attemptCount > 50) {
        errors.push('Deneme sayısı 1-50 arasında olmalıdır');
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        message: errors.join(', '),
        errors,
      });
    }

    // Prepare update data
    const updateData = {
      title,
      description,
      startDate,
      endDate,
      requirements,
      prizes,
      channelRequirement,
      status,
    };

    // Add type-specific data
    if (contest.type === 'SPORTS_PREDICTION') {
      updateData.questions = questions;
      updateData.minCorrectAnswers = minCorrectAnswers;
    } else if (contest.type === 'EMOJI_GAME') {
      updateData.emojiGame = emojiGame;
    }

    // Update contest
    contest = await Contest.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      contest,
    });
  } catch (error) {
    console.error('Update contest error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete contest
// @route   DELETE /api/contests/:id
// @access  Private/Admin
exports.deleteContest = async (req, res) => {
  try {
    const contest = await Contest.findById(req.params.id);

    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    // Check if contest has submissions
    const submissionsCount = await Submission.countDocuments({
      contest: req.params.id,
    });

    if (submissionsCount > 0) {
      return res.status(400).json({
        message:
          'Cannot delete contest with submissions. Change status to CANCELLED instead.',
      });
    }

    await contest.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Contest deleted',
    });
  } catch (error) {
    console.error('Delete contest error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Set correct answers for a contest
// @route   PUT /api/contests/:id/set-answers
// @access  Private/Admin
exports.setCorrectAnswers = async (req, res) => {
  try {
    const { correctAnswers } = req.body;
    const contestId = req.params.id;

    const contest = await Contest.findById(contestId);

    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    // Update correct answers for each question
    contest.questions.forEach((question, index) => {
      if (correctAnswers[index]) {
        question.correctAnswer = correctAnswers[index];
      }
    });

    await contest.save();

    res.status(200).json({
      success: true,
      message: 'Correct answers set successfully',
    });
  } catch (error) {
    console.error('Set correct answers error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Complete a contest and calculate winners
// @route   PUT /api/contests/:id/complete
// @access  Private/Admin
exports.completeContest = async (req, res) => {
  try {
    const contestId = req.params.id;

    const contest = await Contest.findById(contestId);

    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    if (contest.status === 'COMPLETED') {
      return res.status(400).json({
        message: 'Contest is already completed',
      });
    }

    // Get all submissions for this contest
    const submissions = await Submission.find({ contest: contestId }).populate('user');

    if (contest.type === 'SPORTS_PREDICTION') {
      // Check if all questions have correct answers
      const missingAnswers = contest.questions.some(
        (question) => !question.correctAnswer
      );

      if (missingAnswers) {
        return res.status(400).json({
          message: 'All questions must have correct answers before completing the contest',
        });
      }

      // Calculate correct answers for each submission
      for (const submission of submissions) {
        let correctCount = 0;

        contest.questions.forEach((question, index) => {
          const questionId = question._id.toString();
          const userAnswer = submission.answers.get(questionId);

          if (userAnswer === question.correctAnswer) {
            correctCount++;
          }
        });

        submission.correctAnswers = correctCount;

        // Determine if user is a winner
        submission.isWinner = correctCount >= contest.minCorrectAnswers;

        await submission.save();
      }
    } else if (contest.type === 'EMOJI_GAME') {
      // For emoji games, winners are already determined during gameplay
      // Just ensure all submissions are properly marked
      for (const submission of submissions) {
        if (submission.emojiResults && submission.emojiResults.achievedTarget !== undefined) {
          submission.isWinner = submission.emojiResults.achievedTarget;
          await submission.save();
        }
      }
    }

    // Update contest status
    contest.status = 'COMPLETED';
    await contest.save();

    // Notify winners
    const winners = submissions.filter(s => s.isWinner);
    if (winners.length > 0) {
      console.log('Notifying winners...');
      bot.notifyWinners(contestId);
    }

    res.status(200).json({
      success: true,
      message: 'Contest completed and winners calculated',
    });
  } catch (error) {
    console.error('Complete contest error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get contest submissions
// @route   GET /api/contests/:id/submissions
// @access  Private/Admin
exports.getContestSubmissions = async (req, res) => {
  try {
    const contestId = req.params.id;

    const contest = await Contest.findById(contestId);
    if (!contest) {
      return res.status(404).json({ message: 'Contest not found' });
    }

    const submissions = await Submission.find({ contest: contestId })
      .populate('user', 'username telegramId firstName lastName')
      .sort({ correctAnswers: -1, submittedAt: 1 });

    res.status(200).json({
      success: true,
      count: submissions.length,
      submissions,
    });
  } catch (error) {
    console.error('Get contest submissions error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Export contest submissions to Excel
// @route   GET /api/contests/:id/export
// @access  Public (with token)
exports.exportContestSubmissions = async (req, res) => {
  // Check for token in query parameter
  const token = req.query.token;

  if (!token) {
    return res.status(401).json({ message: 'Yetkilendirme hatası. Token bulunamadı.' });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get admin from token
    const admin = await Admin.findById(decoded.id).select('-password');

    if (!admin) {
      return res.status(401).json({ message: 'Geçersiz token. Yönetici bulunamadı.' });
    }

    // Continue with export
    try {
      const contestId = req.params.id;

      const contest = await Contest.findById(contestId);
      if (!contest) {
        return res.status(404).json({ message: 'Yarışma bulunamadı' });
      }

      const submissions = await Submission.find({ contest: contestId })
        .populate('user', 'username telegramId firstName lastName')
        .sort({ correctAnswers: -1, submittedAt: 1 });

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Submissions');

      // Create headers array based on contest type
      let headers = [
        'Kullanıcı Adı',
        'Telegram ID',
        'İsim',
        'Kazanan',
        'Gönderim Tarihi'
      ];

      if (contest.type === 'SPORTS_PREDICTION') {
        // Add correct answers column for sports prediction
        headers.splice(3, 0, 'Doğru Cevaplar');

        // Add question headers
        contest.questions.forEach((question, index) => {
          headers.push(`Soru ${index + 1}: ${question.text.substring(0, 20)}...`);
        });
      } else if (contest.type === 'EMOJI_GAME') {
        // Add emoji game specific headers
        headers.splice(3, 0, 'Oyun Tipi', 'Toplam Deneme', 'Başarılı Deneme', 'Final Skoru', 'Hedef Tutturuldu');

        // Add attempt details headers
        const maxAttempts = contest.emojiGame?.attemptCount || 0;
        for (let i = 1; i <= maxAttempts; i++) {
          headers.push(`${i}. Deneme`);
        }
      }

      // Add header row
      worksheet.addRow(headers);

      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };

      // Add data rows
      submissions.forEach((submission) => {
        let rowData = [
          submission.user.username,
          submission.user.telegramId,
          `${submission.user.firstName || ''} ${submission.user.lastName || ''}`.trim()
        ];

        if (contest.type === 'SPORTS_PREDICTION') {
          rowData.push(
            submission.correctAnswers,
            submission.isWinner ? 'Evet' : 'Hayır',
            new Date(submission.submittedAt).toLocaleString('tr-TR')
          );

          // Add answers
          contest.questions.forEach((question) => {
            const questionId = question._id.toString();
            const userAnswer = submission.answers.get(questionId) || 'Cevap yok';

            // Find the option text for this answer
            const option = question.options.find(opt => opt.value === userAnswer);
            rowData.push(option ? option.text : userAnswer);
          });
        } else if (contest.type === 'EMOJI_GAME') {
          // Get game type name
          const gameTypeNames = {
            'DICE': '🎲 Zar',
            'BASKETBALL': '🏀 Basketbol',
            'FOOTBALL': '⚽ Futbol',
            'DART': '🎯 Dart',
            'BOWLING': '🎳 Bowling',
            'SLOT': '🎰 Slot'
          };
          const gameTypeName = gameTypeNames[submission.emojiResults?.gameType] || 'Bilinmiyor';

          const totalAttempts = submission.emojiResults?.attempts?.length || 0;
          const successfulAttempts = submission.emojiResults?.attempts?.filter(a => a.success).length || 0;
          const finalScore = submission.emojiResults?.totalScore || 0;
          const targetAchieved = submission.emojiResults?.achievedTarget ? 'Evet' : 'Hayır';

          rowData.push(
            gameTypeName,
            totalAttempts,
            successfulAttempts,
            finalScore,
            targetAchieved,
            submission.isWinner ? 'Evet' : 'Hayır',
            new Date(submission.submittedAt).toLocaleString('tr-TR')
          );

          // Add attempt details
          const maxAttempts = contest.emojiGame?.attemptCount || 0;
          for (let i = 0; i < maxAttempts; i++) {
            const attempt = submission.emojiResults?.attempts?.[i];
            if (attempt) {
              let attemptText = `${attempt.result}`;
              if (attempt.success) {
                attemptText += ' ✅';
              }
              rowData.push(attemptText);
            } else {
              rowData.push('-');
            }
          }
        }

        worksheet.addRow(rowData);
      });

      // Set column widths
      worksheet.columns.forEach(column => {
        column.width = 20;
      });

      // Set response headers
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=contest-${contestId}-submissions.xlsx`
      );

      // Write to response
      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      console.error('Export contest submissions error:', error);
      res.status(500).json({ message: 'Sunucu hatası' });
    }
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({ message: 'Geçersiz token' });
  }
};

// @desc    Activate a draft contest
// @route   PUT /api/contests/:id/activate
// @access  Private/Admin
exports.activateContest = async (req, res) => {
  try {
    const contestId = req.params.id;

    const contest = await Contest.findById(contestId);

    if (!contest) {
      return res.status(404).json({ message: 'Yarışma bulunamadı' });
    }

    if (contest.status !== 'DRAFT') {
      return res.status(400).json({
        message: 'Sadece taslak durumdaki yarışmalar aktifleştirilebilir',
      });
    }

    // Update contest status
    contest.status = 'ACTIVE';
    await contest.save();

    res.status(200).json({
      success: true,
      message: 'Yarışma başarıyla aktifleştirildi',
      contest,
    });
  } catch (error) {
    console.error('Activate contest error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Get contest winners
// @route   GET /api/contests/:id/winners
// @access  Private/Admin
exports.getContestWinners = async (req, res) => {
  try {
    const contestId = req.params.id;

    const contest = await Contest.findById(contestId);
    if (!contest) {
      return res.status(404).json({ message: 'Yarışma bulunamadı' });
    }

    if (contest.status !== 'COMPLETED') {
      return res.status(400).json({
        message: 'Sadece tamamlanmış yarışmaların kazananları görüntülenebilir',
      });
    }

    // Get winning submissions
    const winningSubmissions = await Submission.find({
      contest: contestId,
      isWinner: true
    }).populate('user', 'telegramId username firstName lastName');

    res.status(200).json({
      success: true,
      count: winningSubmissions.length,
      winners: winningSubmissions,
    });
  } catch (error) {
    console.error('Get contest winners error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Get submission details with answers
// @route   GET /api/contests/submissions/:id
// @access  Private/Admin
exports.getSubmissionDetails = async (req, res) => {
  try {
    const submissionId = req.params.id;

    const submission = await Submission.findById(submissionId)
      .populate('user', 'telegramId username firstName lastName')
      .populate('contest');

    if (!submission) {
      return res.status(404).json({ message: 'Katılım bulunamadı' });
    }

    // Get contest with questions
    const contest = await Contest.findById(submission.contest._id);
    if (!contest) {
      return res.status(404).json({ message: 'Yarışma bulunamadı' });
    }

    // Create a detailed response based on contest type
    let detailedAnswers = [];

    if (contest.type === 'SPORTS_PREDICTION') {
      // Convert Map to object for easier handling
      const answersObj = submission.answers instanceof Map
        ? Object.fromEntries(submission.answers)
        : submission.answers;

      // Process each question
      contest.questions.forEach((question, index) => {
        const questionId = question._id.toString();
        const userAnswer = answersObj[questionId];

        // Find the selected option text
        let userAnswerText = '';
        let isCorrect = false;

        if (userAnswer) {
          const selectedOption = question.options.find(opt => opt.value === userAnswer);
          userAnswerText = selectedOption ? selectedOption.text : userAnswer;
          isCorrect = question.correctAnswer === userAnswer;
        }

        // Find correct answer text
        let correctAnswerText = '';
        if (question.correctAnswer) {
          const correctOption = question.options.find(opt => opt.value === question.correctAnswer);
          correctAnswerText = correctOption ? correctOption.text : question.correctAnswer;
        }

        detailedAnswers.push({
          questionNumber: index + 1,
          questionText: question.text,
          userAnswer,
          userAnswerText,
          correctAnswer: question.correctAnswer,
          correctAnswerText,
          isCorrect
        });
      });
    }

    res.status(200).json({
      success: true,
      submission: {
        ...submission.toObject(),
        detailedAnswers
      }
    });
  } catch (error) {
    console.error('Get submission details error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Cancel a contest
// @route   PUT /api/contests/:id/cancel
// @access  Private/Admin
exports.cancelContest = async (req, res) => {
  try {
    const contestId = req.params.id;
    const { reason } = req.body;

    const contest = await Contest.findById(contestId);
    if (!contest) {
      return res.status(404).json({ message: 'Yarışma bulunamadı' });
    }

    if (contest.status !== 'ACTIVE' && contest.status !== 'DRAFT') {
      return res.status(400).json({
        message: 'Sadece aktif veya taslak durumdaki yarışmalar iptal edilebilir',
      });
    }

    // Update contest status
    contest.status = 'CANCELLED';
    contest.cancelledAt = new Date();
    contest.cancelReason = reason || '';
    contest.cancelledBy = req.admin._id;

    await contest.save();

    res.status(200).json({
      success: true,
      message: 'Yarışma başarıyla iptal edildi',
    });
  } catch (error) {
    console.error('Cancel contest error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};