const express = require('express');
const router = express.Router();
const {
  getAdmins,
  getAdmin,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  changePassword,
} = require('../controllers/admin.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

// All routes are protected
router.use(protect);

// Routes accessible by superadmin only
router.route('/')
  .get(authorize('superadmin'), getAdmins)
  .post(authorize('superadmin'), createAdmin);

// Routes accessible by superadmin or self
router.route('/:id')
  .get(getAdmin)
  .put(updateAdmin)
  .delete(authorize('superadmin'), deleteAdmin);

router.put('/:id/change-password', changePassword);

module.exports = router;
