const express = require('express');
const router = express.Router();
const {
  createContest,
  getContests,
  getContest,
  updateContest,
  deleteContest,
  setCorrectAnswers,
  completeContest,
  getContestSubmissions,
  exportContestSubmissions,
  activateContest,
  getContestWinners,
  cancelContest,
  getSubmissionDetails,
} = require('../controllers/contest.controller');
const { protect } = require('../middleware/auth.middleware');

// Export route is not protected (will be handled in the controller)
router.get('/:id/export', exportContestSubmissions);

// All other routes are protected
router.use(protect);

router.route('/')
  .get(getContests)
  .post(createContest);

router.route('/:id')
  .get(getContest)
  .put(updateContest)
  .delete(deleteContest);

router.put('/:id/set-answers', setCorrectAnswers);
router.put('/:id/complete', completeContest);
router.put('/:id/activate', activateContest);
router.put('/:id/cancel', cancelContest);
router.get('/:id/submissions', getContestSubmissions);
router.get('/:id/winners', getContestWinners);
router.get('/submissions/:id', getSubmissionDetails);

module.exports = router;
