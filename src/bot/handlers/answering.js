const Contest = require('../../models/Contest');
const { sendQuestion } = require('./contestParticipation');
const { getTranslation } = require('../translations');

// Handle answer selection
const handleAnswering = async (bot, callbackQuery, user, questionId, optionValue) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Check if user is in answering state
    if (user.state !== 'ANSWERING') {
      const translation = await getTranslation(user.language, 'bot.answering.noActiveSubmission');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Get contest
    const contest = await Contest.findById(user.currentContest);

    if (!contest || contest.status !== 'ACTIVE') {
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentQuestionIndex = 0;
      user.tempAnswers = new Map();
      await user.save();
      const translation = await getTranslation(user.language, 'bot.answering.contestNotActive');

      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Save answer
    user.tempAnswers.set(questionId, optionValue);

    // Move to next question
    user.currentQuestionIndex += 1;
    await user.save();

    // Edit the original message to remove buttons
    bot.editMessageReplyMarkup(
      { inline_keyboard: [] },
      {
        chat_id: chatId,
        message_id: callbackQuery.message.message_id,
      }
    );

    // Emojiler ekleyelim
    const selectionEmoji = "🎯"; // Seçim için emoji
    let yourSelectionTranslated = await getTranslation(user.language, 'bot.answering.selected', { option: optionValue });

    // Send feedback for the selected option
    const question = contest.questions.find(q => q._id.toString() === questionId);
    if (question) {
      const option = question.options.find(opt => opt.value === optionValue);
      if (option) {
        // Seçim mesajını gönder ve mesajın gönderilmesini bekle
        await bot.sendMessage(
          chatId,
          `${selectionEmoji} ${yourSelectionTranslated} ${option.text}`
        );

        // Kısa bir gecikme ekleyelim
        await new Promise(resolve => setTimeout(resolve, 500));

        // Sonra bir sonraki soruyu gönder
        await sendQuestion(bot, chatId, user, contest);
      } else {
        // Eğer seçenek bulunamazsa, yine de bir sonraki soruya geç
        await sendQuestion(bot, chatId, user, contest);
      }
    } else {
      // Soru bulunamazsa, bir sonraki soruya geç
      await sendQuestion(bot, chatId, user, contest);
    }
  } catch (error) {
    console.error('Error handling answer:', error);

    // Send error message
    let errorTranslation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(
      chatId,
      `❌ ${errorTranslation}`
    );
  }
};

module.exports = { handleAnswering };
