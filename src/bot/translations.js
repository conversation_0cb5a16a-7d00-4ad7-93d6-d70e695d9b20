const fs = require('fs');
const path = require('path');

// Cache for translations
const translationsCache = {};

// Load translations from JSON file
const loadTranslations = (language) => {
  if (translationsCache[language]) {
    return translationsCache[language];
  }

  try {
    const filePath = path.join(__dirname, '../../public/locales', language, 'translation.json');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(fileContent);
    
    translationsCache[language] = translations;
    return translations;
  } catch (error) {
    console.error(`Error loading translations for ${language}:`, error);
    
    // Fallback to English if the requested language is not available
    if (language !== 'en') {
      return loadTranslations('en');
    }
    
    // Return empty object if even English is not available
    return {};
  }
};

// Get translation by key
const getTranslation = async (language = 'tr', key, params = {}) => {
  const translations = loadTranslations(language);
  
  // Split the key by dots to navigate the nested structure
  const keys = key.split('.');
  let value = translations;
  
  // Navigate through the nested structure
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Key not found, fallback to English
      if (language !== 'en') {
        return getTranslation('en', key, params);
      }
      
      // If even English doesn't have it, return the key
      return key;
    }
  }
  
  // If value is not a string, return the key
  if (typeof value !== 'string') {
    return key;
  }
  
  // Replace parameters in the translation
  let result = value;
  for (const [param, replacement] of Object.entries(params)) {
    result = result.replace(new RegExp(`{{${param}}}`, 'g'), replacement);
  }
  
  return result;
};

module.exports = {
  getTranslation,
};
