// Bildirim Yönetimi JavaScript Dosyası

// Bildirim listesini yükle
async function loadNotifications(page = 1, limit = 10, status = '') {
  try {
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...(status && { status })
    }).toString();

    const response = await fetch(`/api/notifications?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirimler yüklenirken hata oluştu');
    }

    const data = await response.json();
    renderNotificationList(data.notifications, data.pagination);
  } catch (error) {
    console.error('Bildirim yükleme hatası:', error);
    showAlert('error', 'Bildirimler yüklenirken hata oluştu: ' + error.message);
  }
}

// Bildirim listesini render et
function renderNotificationList(notifications, pagination) {
  const tableBody = document.getElementById('notificationsTableBody');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  if (notifications.length === 0) {
    tableBody.innerHTML = `
      <tr>
        <td colspan="7" class="text-center">Bildirim bulunamadı</td>
      </tr>
    `;
    return;
  }

  notifications.forEach(notification => {
    const statusBadge = getStatusBadge(notification.status);
    const row = document.createElement('tr');

    row.innerHTML = `
      <td>${notification.title}</td>
      <td>${statusBadge}</td>
      <td>${notification.totalRecipients}</td>
      <td>${notification.successCount}</td>
      <td>${notification.failedCount}</td>
      <td>${new Date(notification.createdAt).toLocaleString('tr-TR')}</td>
      <td>
        <div class="btn-group">
          <button class="btn btn-sm btn-info view-notification" data-id="${notification._id}">
            <i class="fas fa-eye"></i>
          </button>
          ${notification.status === 'DRAFT' ? `
            <button class="btn btn-sm btn-primary send-notification" data-id="${notification._id}">
              <i class="fas fa-paper-plane"></i>
            </button>
          ` : ''}
          ${notification.status === 'SENDING' ? `
            <button class="btn btn-sm btn-warning pause-notification" data-id="${notification._id}">
              <i class="fas fa-pause"></i>
            </button>
            <button class="btn btn-sm btn-danger stop-notification" data-id="${notification._id}">
              <i class="fas fa-stop"></i>
            </button>
          ` : ''}
          ${notification.status === 'PAUSED' ? `
            <button class="btn btn-sm btn-success resume-notification" data-id="${notification._id}">
              <i class="fas fa-play"></i>
            </button>
            <button class="btn btn-sm btn-danger stop-notification" data-id="${notification._id}">
              <i class="fas fa-stop"></i>
            </button>
          ` : ''}
        </div>
      </td>
    `;

    tableBody.appendChild(row);
  });

  // Sayfalama
  renderPagination(pagination);

  // Olay dinleyicileri ekle
  addNotificationEventListeners();
}

// Durum badge'i oluştur
function getStatusBadge(status) {
  const statusMap = {
    'DRAFT': '<span class="badge badge-secondary">Taslak</span>',
    'SCHEDULED': '<span class="badge badge-info">Zamanlandı</span>',
    'SENDING': '<span class="badge badge-primary">Gönderiliyor</span>',
    'PAUSED': '<span class="badge badge-warning">Duraklatıldı</span>',
    'COMPLETED': '<span class="badge badge-success">Tamamlandı</span>',
    'FAILED': '<span class="badge badge-danger">Başarısız</span>'
  };

  return statusMap[status] || `<span class="badge badge-secondary">${status}</span>`;
}

// Sayfalama oluştur
function renderPagination(pagination) {
  const paginationEl = document.getElementById('notificationsPagination');
  if (!paginationEl) return;

  paginationEl.innerHTML = '';

  if (pagination.total <= pagination.limit) return;

  const totalPages = pagination.pages;
  const currentPage = pagination.page;

  const ul = document.createElement('ul');
  ul.className = 'pagination justify-content-center';

  // Önceki sayfa
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `
    <a class="page-link" href="#" data-page="${currentPage - 1}">
      <i class="fas fa-chevron-left"></i>
    </a>
  `;
  ul.appendChild(prevLi);

  // Sayfa numaraları
  for (let i = 1; i <= totalPages; i++) {
    if (
      i === 1 ||
      i === totalPages ||
      (i >= currentPage - 1 && i <= currentPage + 1)
    ) {
      const li = document.createElement('li');
      li.className = `page-item ${i === currentPage ? 'active' : ''}`;
      li.innerHTML = `
        <a class="page-link" href="#" data-page="${i}">${i}</a>
      `;
      ul.appendChild(li);
    } else if (
      i === currentPage - 2 ||
      i === currentPage + 2
    ) {
      const li = document.createElement('li');
      li.className = 'page-item disabled';
      li.innerHTML = '<a class="page-link" href="#">...</a>';
      ul.appendChild(li);
    }
  }

  // Sonraki sayfa
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `
    <a class="page-link" href="#" data-page="${currentPage + 1}">
      <i class="fas fa-chevron-right"></i>
    </a>
  `;
  ul.appendChild(nextLi);

  paginationEl.appendChild(ul);

  // Sayfalama olaylarını ekle
  paginationEl.querySelectorAll('.page-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const page = e.target.closest('.page-link').dataset.page;
      if (page) {
        loadNotifications(page);
      }
    });
  });
}

// Bildirim olay dinleyicileri ekle
function addNotificationEventListeners() {
  // Bildirim detaylarını görüntüle
  document.querySelectorAll('.view-notification').forEach(button => {
    button.addEventListener('click', () => {
      const notificationId = button.dataset.id;
      viewNotificationDetails(notificationId);
    });
  });

  // Bildirim gönder
  document.querySelectorAll('.send-notification').forEach(button => {
    button.addEventListener('click', () => {
      const notificationId = button.dataset.id;
      sendNotification(notificationId);
      loadNotifications();
    });
  });

  // Bildirim duraklat
  document.querySelectorAll('.pause-notification').forEach(button => {
    button.addEventListener('click', () => {
      const notificationId = button.dataset.id;
      pauseNotification(notificationId);
    });
  });

  // Bildirim devam ettir
  document.querySelectorAll('.resume-notification').forEach(button => {
    button.addEventListener('click', () => {
      const notificationId = button.dataset.id;
      resumeNotification(notificationId);
    });
  });

  // Bildirim durdur
  document.querySelectorAll('.stop-notification').forEach(button => {
    button.addEventListener('click', () => {
      const notificationId = button.dataset.id;
      stopNotification(notificationId);
    });
  });
}

// Bildirim detaylarını görüntüle
async function viewNotificationDetails(notificationId) {
  try {
    const response = await fetch(`/api/notifications/${notificationId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirim detayları alınırken hata oluştu');
    }

    const notification = await response.json();
    showNotificationDetailsModal(notification);
  } catch (error) {
    console.error('Bildirim detayları görüntüleme hatası:', error);
    showAlert('error', 'Bildirim detayları görüntülenirken hata oluştu: ' + error.message);
  }
}

// Bildirim detayları modalını göster
function showNotificationDetailsModal(notification) {
  const modal = document.getElementById('notificationDetailsModal');
  if (!modal) return;

  const modalTitle = modal.querySelector('.modal-title');
  const modalBody = modal.querySelector('.modal-body');

  modalTitle.textContent = `Bildirim Detayları: ${notification.title}`;

  const statusBadge = getStatusBadge(notification.status);
  const createdAt = new Date(notification.createdAt).toLocaleString('tr-TR');
  const startedAt = notification.startedAt ? new Date(notification.startedAt).toLocaleString('tr-TR') : '-';
  const completedAt = notification.completedAt ? new Date(notification.completedAt).toLocaleString('tr-TR') : '-';

  // URL'leri hazırla
  let urlsHtml = '';
  if (notification.urls && notification.urls.length > 0) {
    urlsHtml = '<h5 class="mt-3">URL\'ler</h5><ul>';
    notification.urls.forEach(url => {
      urlsHtml += `
        <li>
          <strong>Orijinal URL:</strong> ${url.originalUrl}<br>
          <strong>Kısa Kod:</strong> ${url.shortCode}<br>
          <strong>Tıklama Sayısı:</strong> ${url.clicks}
        </li>
      `;
    });
    urlsHtml += '</ul>';
  }

  // Butonları hazırla
  let buttonsHtml = '';
  if (notification.buttons && notification.buttons.length > 0) {
    buttonsHtml = '<h5 class="mt-3">Butonlar</h5><ul>';
    notification.buttons.forEach(button => {
      buttonsHtml += `
        <li>
          <strong>Buton Metni:</strong> ${button.text}<br>
          <strong>Buton URL:</strong> ${button.url}<br>
          <strong>Tıklama Sayısı:</strong> ${button.clicks || 0}
        </li>
      `;
    });
    buttonsHtml += '</ul>';
  }

  // İstatistikleri hazırla
  const statsHtml = `
    <div class="row mt-3">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body text-center">
            <h5>Toplam</h5>
            <h3>${notification.totalRecipients}</h3>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <h5>Başarılı</h5>
            <h3>${notification.successCount}</h3>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-danger text-white">
          <div class="card-body text-center">
            <h5>Başarısız</h5>
            <h3>${notification.failedCount}</h3>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <h5>Bekleyen</h5>
            <h3>${notification.pendingCount}</h3>
          </div>
        </div>
      </div>
    </div>
  `;

  // Mesaj önizleme
  const previewButton = `
    <button class="btn btn-info mt-3" id="previewNotificationBtn" data-id="${notification._id}">
      Mesaj Önizleme
    </button>
  `;

  modalBody.innerHTML = `
    <div class="notification-details">
      <p><strong>Durum:</strong> ${statusBadge}</p>
      <p><strong>Oluşturulma Tarihi:</strong> ${createdAt}</p>
      <p><strong>Başlama Tarihi:</strong> ${startedAt}</p>
      <p><strong>Tamamlanma Tarihi:</strong> ${completedAt}</p>
      <p><strong>Oluşturan:</strong> ${notification.createdBy ? notification.createdBy.name : '-'}</p>

      <h5>Mesaj</h5>
      <div class="card">
        <div class="card-body">
          <p>${notification.message.replace(/\n/g, '<br>')}</p>
        </div>
      </div>

      ${urlsHtml}

      ${buttonsHtml}

      <h5 class="mt-3">İstatistikler</h5>
      ${statsHtml}

      ${previewButton}
    </div>
  `;

  // Önizleme butonuna olay dinleyicisi ekle
  const previewBtn = modalBody.querySelector('#previewNotificationBtn');
  if (previewBtn) {
    previewBtn.addEventListener('click', () => {
      previewNotification(notification._id);
    });
  }

  // Modalı göster
  $(modal).modal('show');
}

// Bildirim önizleme
async function previewNotification(notificationId) {
  try {
    const response = await fetch(`/api/notifications/${notificationId}/preview`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirim önizleme alınırken hata oluştu');
    }

    const preview = await response.json();
    showNotificationPreviewModal(preview);
  } catch (error) {
    console.error('Bildirim önizleme hatası:', error);
    showAlert('error', 'Bildirim önizleme alınırken hata oluştu: ' + error.message);
  }
}

// Bildirim önizleme modalını göster
function showNotificationPreviewModal(preview) {
  const modal = document.getElementById('notificationPreviewModal');
  if (!modal) return;

  const modalTitle = modal.querySelector('.modal-title');
  const modalBody = modal.querySelector('.modal-body');

  modalTitle.textContent = `Bildirim Önizleme: ${preview.title}`;

  // Butonları hazırla
  let buttonsHtml = '';
  if (preview.buttons && preview.buttons.length > 0) {
    buttonsHtml = '<div class="mt-3"><h6>Butonlar:</h6><div class="d-flex flex-wrap">';
    preview.buttons.forEach(button => {
      buttonsHtml += `
        <a href="${button.url}" target="_blank" class="btn btn-primary m-1">
          ${button.text}
        </a>
      `;
    });
    buttonsHtml += '</div></div>';
  }

  // Fotoğraf hazırla
  let imageHtml = '';
  if (preview.image) {
    imageHtml = `
      <div class="card mt-3">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Fotoğraf</h5>
        </div>
        <div class="card-body text-center">
          <img src="${preview.image}" class="img-fluid" style="max-height: 300px;">
        </div>
      </div>
    `;
  }

  modalBody.innerHTML = `
    <div class="notification-preview">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Telegram Mesajı</h5>
        </div>
        <div class="card-body">
          <p>${preview.message.replace(/\n/g, '<br>')}</p>
          ${buttonsHtml}
        </div>
      </div>
      ${imageHtml}
    </div>
  `;

  // Modalı göster
  $(modal).modal('show');
}

// Bildirim önizleme (form verilerinden)
function showNotificationPreview(formData) {
  console.log('showNotificationPreview çağrıldı:', formData);

  const modal = document.getElementById('previewNotificationModal');
  if (!modal) {
    console.error('previewNotificationModal bulunamadı');
    return;
  }

  const previewMessageContent = document.getElementById('previewMessageContent');
  const previewImageContainer = document.getElementById('previewImageContainer');
  const previewImage = document.getElementById('previewImage');

  if (!previewMessageContent) {
    console.error('previewMessageContent bulunamadı');
    return;
  }

  // Mesaj içeriğini ayarla
  previewMessageContent.innerHTML = formData.message.replace(/\n/g, '<br>');

  // Butonları hazırla
  let buttonsHtml = '';
  if (formData.buttons && formData.buttons.length > 0) {
    console.log('Önizleme için butonlar hazırlanıyor:', formData.buttons);
    buttonsHtml = '<div class="mt-3"><div class="d-flex flex-wrap">';
    formData.buttons.forEach(button => {
      buttonsHtml += `
        <a href="${button.url}" target="_blank" class="btn btn-primary m-1">
          ${button.text}
        </a>
      `;
    });
    buttonsHtml += '</div></div>';
    previewMessageContent.innerHTML += buttonsHtml;
  }

  // Fotoğraf varsa göster
  if (previewImageContainer && previewImage) {
    const imageInput = document.getElementById('notificationImage');
    const imageFile = imageInput && imageInput.files && imageInput.files.length > 0 ? imageInput.files[0] : null;

    if (imageFile) {
      console.log('Önizleme için fotoğraf yükleniyor');
      const reader = new FileReader();
      reader.onload = function (e) {
        previewImage.src = e.target.result;
        previewImageContainer.classList.remove('d-none');
      };
      reader.readAsDataURL(imageFile);
    } else {
      previewImageContainer.classList.add('d-none');
    }
  }

  // Modalı göster
  console.log('Önizleme modalı gösteriliyor');
  $(modal).modal('show');
}

// Bildirim gönder
async function sendNotification(notificationId) {
  try {
    if (!confirm('Bildirimi göndermek istediğinize emin misiniz?')) {
      return;
    }

    const response = await fetch(`/api/notifications/${notificationId}/send`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Bildirim gönderilirken hata oluştu');
    }

    const data = await response.json();
    showAlert('success', data.message);
    loadNotifications();
  } catch (error) {
    console.error('Bildirim gönderme hatası:', error);
    showAlert('error', 'Bildirim gönderilirken hata oluştu: ' + error.message);
  }
}

// Bildirim duraklat
async function pauseNotification(notificationId) {
  try {
    if (!confirm('Bildirimi duraklatmak istediğinize emin misiniz?')) {
      return;
    }

    const response = await fetch(`/api/notifications/${notificationId}/pause`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirim duraklatılırken hata oluştu');
    }

    const data = await response.json();
    showAlert('success', data.message);
    loadNotifications();
  } catch (error) {
    console.error('Bildirim duraklatma hatası:', error);
    showAlert('error', 'Bildirim duraklatılırken hata oluştu: ' + error.message);
  }
}

// Bildirim devam ettir
async function resumeNotification(notificationId) {
  try {
    if (!confirm('Bildirimi devam ettirmek istediğinize emin misiniz?')) {
      return;
    }

    const response = await fetch(`/api/notifications/${notificationId}/resume`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirim devam ettirilirken hata oluştu');
    }

    const data = await response.json();
    showAlert('success', data.message);
    loadNotifications();
  } catch (error) {
    console.error('Bildirim devam ettirme hatası:', error);
    showAlert('error', 'Bildirim devam ettirilirken hata oluştu: ' + error.message);
  }
}

// Bildirim durdur
async function stopNotification(notificationId) {
  try {
    if (!confirm('Bildirimi durdurmak istediğinize emin misiniz? Bu işlem geri alınamaz.')) {
      return;
    }

    const response = await fetch(`/api/notifications/${notificationId}/stop`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Bildirim durdurulurken hata oluştu');
    }

    const data = await response.json();
    showAlert('success', data.message);
    loadNotifications();
  } catch (error) {
    console.error('Bildirim durdurma hatası:', error);
    showAlert('error', 'Bildirim durdurulurken hata oluştu: ' + error.message);
  }
}

// Yeni bildirim oluştur
async function createNotification(formData) {
  try {
    console.log('createNotification çağrıldı:', formData);

    // Fotoğraf varsa FormData kullan
    const imageInput = document.getElementById('notificationImage');
    const imageFile = imageInput && imageInput.files && imageInput.files.length > 0 ? imageInput.files[0] : null;

    console.log('Fotoğraf:', imageFile);

    if (imageFile) {
      const formDataObj = new FormData();
      formDataObj.append('title', formData.title);
      formDataObj.append('message', formData.message);

      // URL'leri ekle
      if (formData.urls && formData.urls.length > 0) {
        formData.urls.forEach((url, index) => {
          formDataObj.append(`urls[${index}]`, url);
        });
      }

      // Butonları ekle
      if (formData.buttons && formData.buttons.length > 0) {
        console.log('Butonlar ekleniyor:', formData.buttons);
        formDataObj.append('buttons', JSON.stringify(formData.buttons));
      }

      // Fotoğrafı ekle
      formDataObj.append('image', imageFile);

      console.log('FormData gönderiliyor');

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formDataObj
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Bildirim oluşturulurken hata oluştu');
      }

      const data = await response.json();
      showAlert('success', 'Bildirim başarıyla oluşturuldu');
      loadNotifications();
      return data;
    } else {
      // Fotoğraf yoksa JSON kullan
      console.log('JSON gönderiliyor');

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Bildirim oluşturulurken hata oluştu');
      }

      const data = await response.json();
      showAlert('success', 'Bildirim başarıyla oluşturuldu');
      loadNotifications();
      return data;
    }
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    showAlert('error', 'Bildirim oluşturulurken hata oluştu: ' + error.message);
    throw error;
  }
}

// Günlük bildirim limitini kontrol et
async function checkDailyLimit() {
  try {
    const response = await fetch('/api/notifications/limits/daily', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('Limit bilgisi alınırken hata oluştu');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Limit kontrol hatası:', error);
    showAlert('error', 'Limit bilgisi alınırken hata oluştu: ' + error.message);
    return null;
  }
}

// Bildirim oluşturma formunu başlat
function initNotificationForm() {
  const form = document.getElementById('createNotificationForm');
  if (!form) return;

  // Fotoğraf önizleme
  const notificationImage = document.getElementById('notificationImage');
  const imagePreviewContainer = document.getElementById('imagePreviewContainer');
  const imagePreview = document.getElementById('imagePreview');
  const removeImageBtn = document.getElementById('removeImageBtn');

  if (notificationImage && imagePreviewContainer && imagePreview) {
    notificationImage.addEventListener('change', function () {
      if (this.files && this.files[0]) {
        const reader = new FileReader();
        reader.onload = function (e) {
          imagePreview.src = e.target.result;
          imagePreviewContainer.classList.remove('d-none');
        };
        reader.readAsDataURL(this.files[0]);
      }
    });

    if (removeImageBtn) {
      removeImageBtn.addEventListener('click', function () {
        notificationImage.value = '';
        imagePreviewContainer.classList.add('d-none');
      });
    }
  }

  // Bildirim oluşturma butonu
  const createBtn = document.getElementById('createNotificationBtn');
  if (createBtn) {
    createBtn.addEventListener('click', async () => {
      const title = document.getElementById('notificationTitle').value;
      const message = document.getElementById('notificationMessage').value;

      if (!title || !message) {
        showAlert('error', 'Lütfen başlık ve mesaj alanlarını doldurun.');
        return;
      }

      // URL'leri boş dizi olarak ayarla (artık kullanılmıyor)
      const urls = [];

      // Butonları topla
      const buttons = [];
      document.querySelectorAll('.notification-button-group').forEach((group) => {
        const textInput = group.querySelector('.notification-button-text');
        const urlInput = group.querySelector('.notification-button-url');

        if (textInput && urlInput && textInput.value.trim() && urlInput.value.trim()) {
          buttons.push({
            text: textInput.value.trim(),
            url: urlInput.value.trim()
          });
        }
      });

      // Buton sayısını kontrol et
      if (buttons.length === 0 && document.querySelectorAll('.notification-button-group').length > 0) {
        showAlert('warning', 'Eklediğiniz butonların metin ve URL alanlarını doldurun veya butonları kaldırın.');
        return;
      }

      try {
        // Limit kontrolü
        const limitCheck = await checkDailyLimit();
        if (limitCheck && !limitCheck.canSend) {
          showAlert('warning', `Günlük bildirim limitine ulaşıldı. Limit: ${limitCheck.limit}, Gönderilen: ${limitCheck.sent}`);
          return;
        }

        // Önizleme kontrolü
        const previewBeforeSend = document.getElementById('previewBeforeSend').checked;
        if (previewBeforeSend) {
          showNotificationPreview({ title, message, buttons });
        } else {
          await createNotification({ title, message, urls, buttons });
          form.reset();
          $('#createNotificationModal').modal('hide');
        }
      } catch (error) {
        console.error('Form gönderme hatası:', error);
        showAlert('error', 'Bildirim oluşturulurken hata oluştu: ' + error.message);
      }
    });
  }

  // Önizleme sonrası gönderme butonu
  const confirmSendBtn = document.getElementById('confirmSendBtn');
  if (confirmSendBtn) {
    confirmSendBtn.addEventListener('click', async () => {
      const title = document.getElementById('notificationTitle').value;
      const message = document.getElementById('notificationMessage').value;
      const urls = [];

      // Butonları topla
      const buttons = [];
      document.querySelectorAll('.notification-button-group').forEach((group) => {
        const textInput = group.querySelector('.notification-button-text');
        const urlInput = group.querySelector('.notification-button-url');

        if (textInput && urlInput && textInput.value.trim() && urlInput.value.trim()) {
          buttons.push({
            text: textInput.value.trim(),
            url: urlInput.value.trim()
          });
        }
      });

      // Buton sayısını kontrol et
      if (buttons.length === 0 && document.querySelectorAll('.notification-button-group').length > 0) {
        showAlert('warning', 'Eklediğiniz butonların metin ve URL alanlarını doldurun veya butonları kaldırın.');
        return;
      }

      try {
        await createNotification({ title, message, urls, buttons });
        form.reset();
        $('#previewNotificationModal').modal('hide');
        $('#createNotificationModal').modal('hide');
      } catch (error) {
        console.error('Bildirim gönderme hatası:', error);
        showAlert('error', 'Bildirim oluşturulurken hata oluştu: ' + error.message);
      }
    });
  }



  // Buton ekleme butonu
  const addButtonBtn = document.getElementById('addButtonBtn');
  if (addButtonBtn) {
    // Tüm olay dinleyicilerini kaldır
    const oldAddButtonBtn = addButtonBtn;
    const newAddButtonBtn = oldAddButtonBtn.cloneNode(true);
    oldAddButtonBtn.parentNode.replaceChild(newAddButtonBtn, oldAddButtonBtn);

    // Yeni olay dinleyicisi ekle
    newAddButtonBtn.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();

      console.log('Buton ekle butonuna tıklandı');

      const buttonsContainer = document.getElementById('notificationButtons');
      if (!buttonsContainer) {
        console.error('notificationButtons container bulunamadı');
        return;
      }

      const buttonCount = buttonsContainer.querySelectorAll('.notification-button-group').length + 1;

      // Maksimum 10 buton kontrolü
      if (buttonCount > 10) {
        showAlert('warning', 'Maksimum 10 buton ekleyebilirsiniz.');
        return;
      }

      const buttonGroup = document.createElement('div');
      buttonGroup.className = 'notification-button-group mb-3';
      buttonGroup.innerHTML = `
        <div class="input-group">
          <div class="input-group-prepend">
            <span class="input-group-text">Buton ${buttonCount}</span>
          </div>
          <input type="text" class="form-control notification-button-text" placeholder="Buton Metni" maxlength="20">
          <input type="url" class="form-control notification-button-url" placeholder="https://example.com">
          <div class="input-group-append">
            <button type="button" class="btn btn-danger remove-button-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      `;

      buttonsContainer.appendChild(buttonGroup);

      // Buton kaldırma butonu
      const removeBtn = buttonGroup.querySelector('.remove-button-btn');
      if (removeBtn) {
        removeBtn.addEventListener('click', function (e) {
          e.preventDefault();
          e.stopPropagation();
          buttonGroup.remove();
          // Buton sayısını güncelle ve buton ekle butonunu etkinleştir
          updateButtonCount();
        });
      }

      // Buton sayısını kontrol et
      updateButtonCount();
    });

    // Buton sayısını güncelleme ve buton ekle butonunu kontrol etme fonksiyonu
    function updateButtonCount() {
      const buttonsContainer = document.getElementById('notificationButtons');
      if (!buttonsContainer) return;

      const buttonCount = buttonsContainer.querySelectorAll('.notification-button-group').length;

      // Buton sayısı 10'a ulaştıysa buton ekle butonunu devre dışı bırak
      if (buttonCount >= 10) {
        newAddButtonBtn.disabled = true;
        newAddButtonBtn.classList.add('disabled');
      } else {
        newAddButtonBtn.disabled = false;
        newAddButtonBtn.classList.remove('disabled');
      }

      // Buton numaralarını güncelle
      buttonsContainer.querySelectorAll('.notification-button-group').forEach((group, index) => {
        const buttonLabel = group.querySelector('.input-group-text');
        if (buttonLabel) {
          buttonLabel.textContent = `Buton ${index + 1}`;
        }
      });

      console.log(`Buton sayısı güncellendi: ${buttonCount}`);
    }
  }
}

// Bildirim filtreleme
function initNotificationFilters() {
  const statusFilter = document.getElementById('statusFilter');
  if (statusFilter) {
    statusFilter.addEventListener('change', () => {
      loadNotifications(1, 10, statusFilter.value);
    });
  }
}

// Bildirim yenileme
function initNotificationRefresh() {
  const refreshBtn = document.getElementById('refreshNotificationsBtn');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', () => {
      const statusFilter = document.getElementById('statusFilter');
      loadNotifications(1, 10, statusFilter ? statusFilter.value : '');
    });
  }
}

// Uyarı göster
function showAlert(type, message) {
  const alertsContainer = document.getElementById('alertsContainer');
  if (!alertsContainer) return;

  const alert = document.createElement('div');
  alert.className = `alert alert-${type} alert-dismissible fade show`;
  alert.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  `;

  alertsContainer.appendChild(alert);

  // 5 saniye sonra uyarıyı kaldır
  setTimeout(() => {
    alert.classList.remove('show');
    setTimeout(() => {
      alert.remove();
    }, 150);
  }, 5000);
}

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', () => {
  // Bildirim listesini yükle
  if (document.getElementById('notificationsTableBody')) {
    loadNotifications();
  }

});
