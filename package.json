{"name": "telegram-bot-loto", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/index.js", "dev": "nodemon src/index.js", "init-db": "node src/utils/initDb.js"}, "keywords": [], "author": "", "license": "ISC", "description": "Telegram bot for sports prediction contests", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "express-validator": "^7.2.1", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "node-telegram-bot-api": "^0.66.0"}, "devDependencies": {"nodemon": "^3.1.9"}}