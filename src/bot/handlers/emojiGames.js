const Contest = require('../../models/Contest');
const Submission = require('../../models/Submission');
const { getTranslation } = require('../translations');
const { checkChannelMembership } = require('../utils');
const crypto = require('crypto');

// Emoji game constants
const EMOJI_GAMES = {
  DICE: {
    emoji: '🎲',
    telegramEmoji: '🎲',
    values: [1, 2, 3, 4, 5, 6],
  },
  BASKETBALL: {
    emoji: '🏀',
    telegramEmoji: '🏀',
    values: [1, 2, 3, 4, 5], // 1-4 miss, 5 success
  },
  FOOTBALL: {
    emoji: '⚽',
    telegramEmoji: '⚽',
    values: [1, 2, 3, 4, 5], // 1-4 miss, 5 success
  },
  DART: {
    emoji: '🎯',
    telegramEmoji: '🎯',
    values: [1, 2, 3, 4, 5, 6], // 6 is bullseye
  },
  BOWLING: {
    emoji: '🎳',
    telegramEmoji: '🎳',
    values: [1, 2, 3, 4, 5, 6], // 6 is strike
  },
  SLOT: {
    emoji: '🎰',
    telegramEmoji: '🎰',
    symbols: ['🍋', '🍇', '🍊', '7️⃣'], // lemon, grape, orange, seven
  },
};

// Generate secure button token to prevent replay attacks
const generateButtonToken = (submissionId, attemptNumber) => {
  const secret = process.env.JWT_SECRET || 'default-secret';
  // Use submission ID and attempt number for deterministic token
  const data = `${submissionId}-${attemptNumber}`;
  return crypto.createHmac('sha256', secret).update(data).digest('hex').substring(0, 16);
};

// Success messages for each game
const SUCCESS_MESSAGES = {
  DICE: {
    tr: ['🎲 Harika atış!', '🎯 Mükemmel!', '🔥 Süper!'],
    en: ['🎲 Great roll!', '🎯 Perfect!', '🔥 Awesome!'],
    de: ['🎲 Großartiger Wurf!', '🎯 Perfekt!', '🔥 Super!'],
    ar: ['🎲 رمية رائعة!', '🎯 مثالي!', '🔥 رائع!'],
  },
  BASKETBALL: {
    tr: ['🏀 BASKETTTT! 🔥', '🎯 Tam onikiden!', '💪 Harika atış!'],
    en: ['🏀 BASKETTTT! 🔥', '🎯 Perfect shot!', '💪 Great shot!'],
    de: ['🏀 KORB! 🔥', '🎯 Perfekter Schuss!', '💪 Großartiger Schuss!'],
    ar: ['🏀 سلة! 🔥', '🎯 تسديدة مثالية!', '💪 تسديدة رائعة!'],
  },
  FOOTBALL: {
    tr: ['⚽ GOOOOOL! 🔥', '🎯 Tam köşeye!', '💪 Muhteşem gol!'],
    en: ['⚽ GOOOAAL! 🔥', '🎯 Perfect corner!', '💪 Amazing goal!'],
    de: ['⚽ TOOOOR! 🔥', '🎯 Perfekte Ecke!', '💪 Fantastisches Tor!'],
    ar: ['⚽ هدف! 🔥', '🎯 زاوية مثالية!', '💪 هدف رائع!'],
  },
  DART: {
    tr: ['🎯 TAM ONİKİDEN! 🔥', '💥 Bullseye!', '🎪 Mükemmel atış!'],
    en: ['🎯 BULLSEYE! 🔥', '💥 Perfect hit!', '🎪 Amazing throw!'],
    de: ['🎯 VOLLTREFFER! 🔥', '💥 Perfekter Treffer!', '🎪 Fantastischer Wurf!'],
    ar: ['🎯 في المركز! 🔥', '💥 إصابة مثالية!', '🎪 رمية رائعة!'],
  },
  BOWLING: {
    tr: ['🎳 STRIKE! 🔥', '💥 Hepsi devrildi!', '🎪 Mükemmel atış!'],
    en: ['🎳 STRIKE! 🔥', '💥 All pins down!', '🎪 Perfect throw!'],
    de: ['🎳 STRIKE! 🔥', '💥 Alle Pins umgeworfen!', '🎪 Perfekter Wurf!'],
    ar: ['🎳 سترايك! 🔥', '💥 سقطت كلها!', '🎪 رمية مثالية!'],
  },
  SLOT: {
    tr: ['🎰 JACKPOT! 🔥', '💰 Büyük kazanç!', '🎉 Tebrikler!'],
    en: ['🎰 JACKPOT! 🔥', '💰 Big win!', '🎉 Congratulations!'],
    de: ['🎰 JACKPOT! 🔥', '💰 Großer Gewinn!', '🎉 Glückwunsch!'],
    ar: ['🎰 جاكبوت! 🔥', '💰 ربح كبير!', '🎉 تهانينا!'],
  },
};

// Handle emoji game participation
const handleEmojiGameParticipation = async (bot, callbackQuery, user, contestId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get contest
    const contest = await Contest.findById(contestId);

    if (!contest || contest.status !== 'ACTIVE') {
      const translation = await getTranslation(user.language, 'bot.contests.notActive');
      return bot.sendMessage(chatId, translation);
    }

    // Check if contest is emoji game
    if (contest.type !== 'EMOJI_GAME') {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, translation);
    }

    // Check if contest has started and not ended
    const now = new Date();
    if (now < contest.startDate || now > contest.endDate) {
      const translation = await getTranslation(user.language, 'bot.contests.notActive');
      return bot.sendMessage(chatId, translation);
    }

    // Check channel membership if required
    if (contest.channelRequirement.required) {
      const isMember = await checkChannelMembership(bot, user.telegramId, contest.channelRequirement.channelUsername);
      if (!isMember) {
        const translation = await getTranslation(user.language, 'bot.contests.channelRequired', {
          channel: contest.channelRequirement.channelUsername,
        });
        return bot.sendMessage(chatId, translation);
      }
    }

    // Check if user already participated
    const existingSubmission = await Submission.findOne({
      user: user._id,
      contest: contestId,
    });

    if (existingSubmission) {
      const translation = await getTranslation(user.language, 'bot.contests.alreadyParticipated');
      return bot.sendMessage(chatId, translation);
    }

    // Update user state to waiting for game start
    user.state = 'WAITING_EMOJI_GAME_START';
    user.currentContest = contestId;
    await user.save();

    // Show contest details first
    const { formatContestDetails } = require('../utils');
    const contestDetails = await formatContestDetails(contest, user.language);

    await bot.sendMessage(chatId, contestDetails, {
      parse_mode: 'Markdown',
    });

    // Add a small delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Send start button
    const startButtonText = await getTranslation(user.language, 'bot.emojiGames.buttons.startContest');
    const keyboard = {
      inline_keyboard: [
        [
          {
            text: startButtonText,
            callback_data: `start_emoji_game_${contestId}`,
          },
        ],
      ],
    };

    const startPrompt = await getTranslation(user.language, 'bot.emojiGames.startPrompt') || '🎮 Yarışmaya başlamak için aşağıdaki butona tıklayın!';
    await bot.sendMessage(chatId, startPrompt, {
      reply_markup: keyboard,
    });

  } catch (error) {
    console.error('Error handling emoji game participation:', error);

    // Handle specific errors with appropriate messages
    let errorKey = 'bot.errors.general';

    if (error.message && error.message.includes('not found')) {
      errorKey = 'bot.errors.contestNotFound';
    } else if (error.message && error.message.includes('not active')) {
      errorKey = 'bot.errors.contestNotActive';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorKey = 'bot.errors.network';
    } else if (error.name === 'MongoServerError') {
      errorKey = 'bot.errors.database';
    }

    const translation = await getTranslation(user.language, errorKey);
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Start emoji game
const startEmojiGame = async (bot, callbackQuery, user, contestId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get contest
    const contest = await Contest.findById(contestId);
    if (!contest || contest.status !== 'ACTIVE') {
      const translation = await getTranslation(user.language, 'bot.participation.notFound');
      return bot.sendMessage(chatId, translation);
    }

    // Check if user already has a submission for this contest
    const existingSubmission = await Submission.findOne({
      user: user._id,
      contest: contestId,
    });

    if (existingSubmission) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.alreadyParticipated');
      return bot.sendMessage(chatId, translation);
    }

    // Create new submission
    const submission = await Submission.create({
      user: user._id,
      contest: contestId,
      emojiResults: {
        gameType: contest.emojiGame.gameType,
        attempts: [],
        totalScore: 0,
        achievedTarget: false,
      },
    });

    // Update user state
    user.state = 'PLAYING_EMOJI_GAME';
    user.currentContest = contestId;
    user.currentSubmission = submission._id;
    await user.save();

    // Send game instructions
    await sendGameInstructions(bot, chatId, user, contest);

  } catch (error) {
    console.error('Error starting emoji game:', error);

    // Handle specific MongoDB duplicate key error
    if (error.code === 11000 && error.keyPattern && error.keyPattern.user && error.keyPattern.contest) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.alreadyParticipated');
      return bot.sendMessage(chatId, `⚠️ ${translation}`);
    }

    // Handle other errors
    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Send game instructions
const sendGameInstructions = async (bot, chatId, user, contest) => {
  const gameType = contest.emojiGame.gameType;
  const attemptCount = contest.emojiGame.attemptCount;

  let instructions = '';
  let emoji = EMOJI_GAMES[gameType].emoji;

  // Get game-specific instructions
  switch (gameType) {
    case 'DICE':
      if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
        instructions = await getTranslation(user.language, 'bot.emojiGames.dice.totalValue', {
          count: attemptCount,
          target: contest.emojiGame.diceSettings.targetValue,
          emoji: emoji,
        });
      } else {
        instructions = await getTranslation(user.language, 'bot.emojiGames.dice.specificCount', {
          count: attemptCount,
          target: contest.emojiGame.diceSettings.targetCount,
          value: contest.emojiGame.diceSettings.targetValue,
          emoji: emoji,
        });
      }
      break;
    case 'BASKETBALL':
      instructions = await getTranslation(user.language, 'bot.emojiGames.basketball', {
        count: attemptCount,
        target: contest.emojiGame.successTarget,
        emoji: emoji,
      });
      break;
    case 'FOOTBALL':
      instructions = await getTranslation(user.language, 'bot.emojiGames.football', {
        count: attemptCount,
        target: contest.emojiGame.successTarget,
        emoji: emoji,
      });
      break;
    case 'DART':
      instructions = await getTranslation(user.language, 'bot.emojiGames.dart', {
        count: attemptCount,
        target: contest.emojiGame.bullseyeTarget,
        emoji: emoji,
      });
      break;
    case 'BOWLING':
      instructions = await getTranslation(user.language, 'bot.emojiGames.bowling', {
        count: attemptCount,
        target: contest.emojiGame.strikeTarget,
        emoji: emoji,
      });
      break;
    case 'SLOT':
      const combinations = contest.emojiGame.slotSettings.winningCombinations
        .map(combo => `${combo.combination.join('')} - ${combo.name}`)
        .join('\n');
      instructions = await getTranslation(user.language, 'bot.emojiGames.slot', {
        count: attemptCount,
        combinations: combinations,
        emoji: emoji,
      });
      break;
  }

  // Send instructions
  await bot.sendMessage(chatId, instructions, { parse_mode: 'Markdown' });

  // Send first attempt prompt with button
  const startMessage = await getTranslation(user.language, 'bot.emojiGames.start', {
    emoji: emoji,
    attempt: 1,
    total: attemptCount,
  });

  // Get button text based on game type
  const buttonText = await getTranslation(user.language, `bot.emojiGames.buttons.${gameType.toLowerCase()}`);

  // Generate secure token for first attempt
  const firstAttemptToken = generateButtonToken(user.currentSubmission, 1);

  const keyboard = {
    inline_keyboard: [
      [
        {
          text: buttonText,
          callback_data: `play_emoji_${gameType.toLowerCase()}_${user.currentSubmission}_${firstAttemptToken}`,
        },
      ],
    ],
  };

  await bot.sendMessage(chatId, startMessage, {
    reply_markup: keyboard,
    parse_mode: 'Markdown',
  });
};

// In-memory lock to prevent concurrent game plays
const gamePlayLocks = new Map();

// Handle emoji game play (button click)
const handleEmojiGamePlay = async (bot, callbackQuery, user, gameType, submissionId, buttonToken) => {
  const chatId = callbackQuery.message.chat.id;
  const lockKey = `${user._id}_${submissionId}`;

  try {
    // Check if this user/submission is already being processed
    if (gamePlayLocks.has(lockKey)) {
      await bot.answerCallbackQuery(callbackQuery.id, {
        text: '⏳ İşlem devam ediyor, lütfen bekleyin...',
        show_alert: false
      });
      return;
    }

    // Set lock
    gamePlayLocks.set(lockKey, true);

    // Answer callback query immediately to prevent multiple clicks
    const loadingMessages = {
      'DICE': '🎲 Zar atılıyor...',
      'BASKETBALL': '🏀 Atış yapılıyor...',
      'FOOTBALL': '⚽ Şut çekiliyor...',
      'DART': '🎯 Dart atılıyor...',
      'BOWLING': '🎳 Top atılıyor...',
      'SLOT': '🎰 Çevriliyor...'
    };

    await bot.answerCallbackQuery(callbackQuery.id, {
      text: loadingMessages[gameType.toUpperCase()] || '🎮 Oynanıyor...',
      show_alert: false
    });

    // Remove the button from the message immediately after click
    try {
      await bot.editMessageReplyMarkup(
        { inline_keyboard: [] }, // Empty keyboard
        {
          chat_id: chatId,
          message_id: callbackQuery.message.message_id
        }
      );
    } catch (error) {
      // Ignore edit errors (message might be too old)
      console.log('Could not remove button:', error.message);
    }

    // Get submission with fresh data
    const submission = await Submission.findById(submissionId);
    if (!submission) {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Get contest
    const contest = await Contest.findById(submission.contest);
    if (!contest) {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Security check: Verify user state and current submission
    if (user.state !== 'PLAYING_EMOJI_GAME' || user.currentSubmission?.toString() !== submissionId) {
      const translation = await getTranslation(user.language, 'bot.errors.invalidGameState');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Security check: Verify button token to prevent replay attacks
    const expectedAttempt = submission.emojiResults.attempts.length + 1;
    const expectedToken = generateButtonToken(submissionId, expectedAttempt);

    if (buttonToken !== expectedToken) {
      const translation = await getTranslation(user.language, 'bot.errors.invalidGameState');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Check if user has attempts left (double check with fresh data)
    const currentAttempts = submission.emojiResults.attempts.length;
    const maxAttempts = contest.emojiGame.attemptCount;

    if (currentAttempts >= maxAttempts) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.noAttemptsLeft') || 'Deneme hakkınız kalmadı!';
      return bot.sendMessage(chatId, translation);
    }

    // Use Telegram's sendDice API to send real animated emoji
    const gameConfig = EMOJI_GAMES[gameType.toUpperCase()];
    if (!gameConfig) {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Send the animated emoji using Telegram's sendDice API
    const diceMessage = await bot.sendDice(chatId, {
      emoji: gameConfig.telegramEmoji
    });

    // Wait for the animation to complete (Telegram animations take ~3-4 seconds)
    await new Promise(resolve => setTimeout(resolve, 4000));

    // Get the actual result from Telegram's API
    const telegramResult = diceMessage.dice.value;

    // Process the result based on game type
    let result = telegramResult;
    let success = false;
    let score = 0;

    switch (gameType.toUpperCase()) {
      case 'DICE':
        score = result;

        // Check success based on target type
        if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
          const totalScore = submission.emojiResults.totalScore + score;
          success = totalScore >= contest.emojiGame.diceSettings.targetValue;
        } else if (contest.emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT') {
          const specificCount = submission.emojiResults.attempts.filter(a => a.result === contest.emojiGame.diceSettings.targetValue).length;
          if (result === contest.emojiGame.diceSettings.targetValue) {
            success = (specificCount + 1) >= contest.emojiGame.diceSettings.targetCount;
          }
        }
        break;

      case 'BASKETBALL':
        // Telegram basketball: 4 and 5 are successful shots
        success = result === 4 || result === 5;
        score = success ? 1 : 0;
        break;

      case 'FOOTBALL':
        // Telegram football: 4 and 5 are goals
        success = result === 4 || result === 5;
        score = success ? 1 : 0;
        break;

      case 'DART':
        // Telegram dart: 6 is bullseye
        success = result === 6;
        score = success ? 1 : 0;
        break;

      case 'BOWLING':
        // Telegram bowling: 6 is strike
        success = result === 6;
        score = success ? 1 : 0;
        break;

      case 'SLOT':
        // Telegram slot machine: values 1-64
        // Check if this value is in the winning combinations
        success = isSlotWinningCombination(result, contest.emojiGame.slotSettings.winningCombinations);
        score = success ? 1 : 0;
        // Convert to emoji representation for display
        const slotDisplay = convertSlotValueToEmojis(result);
        result = slotDisplay;
        break;
    }

    // Use atomic update to prevent race conditions
    const newAttempt = {
      result: result,
      success: success,
      timestamp: new Date(),
    };

    // Check if target achieved
    let targetAchieved = false;
    const newTotalScore = submission.emojiResults.totalScore + score;

    switch (gameType.toUpperCase()) {
      case 'DICE':
        if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
          // For total value, check if we reached the target
          targetAchieved = newTotalScore >= contest.emojiGame.diceSettings.targetValue;
        } else if (contest.emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT') {
          // For specific value count, check if we got enough of the specific value
          const specificCount = submission.emojiResults.attempts.filter(a => a.result === contest.emojiGame.diceSettings.targetValue).length;
          if (result === contest.emojiGame.diceSettings.targetValue) {
            targetAchieved = (specificCount + 1) >= contest.emojiGame.diceSettings.targetCount;
          }
        }
        break;

      case 'BASKETBALL':
      case 'FOOTBALL':
        targetAchieved = newTotalScore >= contest.emojiGame.successTarget;
        break;

      case 'DART':
        targetAchieved = newTotalScore >= contest.emojiGame.bullseyeTarget;
        break;

      case 'BOWLING':
        targetAchieved = newTotalScore >= contest.emojiGame.strikeTarget;
        break;

      case 'SLOT':
        targetAchieved = success; // Any winning combination
        break;
    }

    // Atomic update to prevent race conditions
    const updatedSubmission = await Submission.findOneAndUpdate(
      {
        _id: submissionId,
        'emojiResults.attempts': { $size: currentAttempts } // Ensure no new attempts were added
      },
      {
        $push: { 'emojiResults.attempts': newAttempt },
        $inc: { 'emojiResults.totalScore': score },
        $set: { 'emojiResults.achievedTarget': targetAchieved }
      },
      { new: true }
    );

    if (!updatedSubmission) {
      // Another request already processed this attempt
      const translation = await getTranslation(user.language, 'bot.errors.invalidGameState');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Update our local submission object
    submission.emojiResults = updatedSubmission.emojiResults;

    // Prepare result message
    const attemptNumber = submission.emojiResults.attempts.length;
    let resultMessage = '';

    switch (gameType.toUpperCase()) {
      case 'DICE':
        resultMessage = `🎲 **${attemptNumber}. Atış:** ${result}`;
        break;

      case 'BASKETBALL':
        if (success) {
          resultMessage = `🏀 **${attemptNumber}. Atış:** Başarılı! 🎉`;
        } else {
          resultMessage = `🏀 **${attemptNumber}. Atış:** Kaçırdı ❌`;
        }
        break;

      case 'FOOTBALL':
        if (success) {
          resultMessage = `⚽ **${attemptNumber}. Şut:** Gol! ⚽🎉`;
        } else {
          resultMessage = `⚽ **${attemptNumber}. Şut:** Kaçırdı ❌`;
        }
        break;

      case 'DART':
        if (success) {
          resultMessage = `🎯 **${attemptNumber}. Atış:** Bullseye! 🎯🎉`;
        } else {
          resultMessage = `🎯 **${attemptNumber}. Atış:** ${result} puan`;
        }
        break;

      case 'BOWLING':
        if (success) {
          resultMessage = `🎳 **${attemptNumber}. Atış:** Strike! 🎳🎉`;
        } else {
          resultMessage = `🎳 **${attemptNumber}. Atış:** Kaçırdı ❌`;
        }
        break;

      case 'SLOT':
        resultMessage = `🎰 **${attemptNumber}. Çevirme:** ${result}`;
        if (success) {
          resultMessage += ' 🎉 **Kazandınız!**';
        }
        break;
    }

    // Check if game is finished
    const hasAttemptsLeft = attemptNumber < maxAttempts;

    if (targetAchieved || !hasAttemptsLeft) {
      // Game finished
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentSubmission = null;
      await user.save();

      // Prepare final message
      let finalMessage = resultMessage + '\n\n';

      // Get target and score information
      const targetInfo = getTargetInfo(gameType, contest);
      const scoreInfo = getScoreInfo(gameType, contest, submission);

      if (targetAchieved) {
        finalMessage += `🎆🎇✨ **TEBRİKLER!** ✨🎇🎆\n🎯 Hedefe ulaştınız!\n`;
        finalMessage += `🎯 Hedef: ${targetInfo}\n`;
        finalMessage += `📊 ${scoreInfo}\n\n`;
        finalMessage += `📝 Katıldığınız için teşekkür ederiz! Sonuçlar yarışma bittiğinde açıklanacaktır.`;
      } else {
        finalMessage += `🎮 Oyun tamamlandı!\n`;
        finalMessage += `🎯 Hedef: ${targetInfo}\n`;
        finalMessage += `📊 ${scoreInfo}\n\n`;
        finalMessage += `📝 Katıldığınız için teşekkür ederiz! Sonuçlar yarışma bittiğinde açıklanacaktır.`;
      }

      // Send single combined message
      await bot.sendMessage(chatId, finalMessage, { parse_mode: 'Markdown' });

    } else {
      // Continue game - combine result and next attempt message
      const gameEmoji = EMOJI_GAMES[gameType.toUpperCase()]?.emoji || '🎮';

      // Show appropriate score based on game type
      const scoreInfo = getScoreInfo(gameType, contest, submission);

      // Get target information
      const targetInfo = getTargetInfo(gameType, contest);

      const combinedMessage = resultMessage + '\n\n' +
        `${gameEmoji} **Bir sonraki denemenizi yapın!**\n` +
        `📊 ${scoreInfo}\n` +
        `🎯 Hedef: ${targetInfo}\n` +
        `🎮 Deneme: ${attemptNumber}/${maxAttempts}`;

      // Get button text
      const buttonText = await getTranslation(user.language, `bot.emojiGames.buttons.${gameType.toLowerCase()}`);

      // Generate secure token for next attempt
      const nextAttemptToken = generateButtonToken(submissionId, attemptNumber + 1);

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: buttonText,
              callback_data: `play_emoji_${gameType.toLowerCase()}_${submissionId}_${nextAttemptToken}`,
            },
          ],
        ],
      };

      await bot.sendMessage(chatId, combinedMessage, {
        reply_markup: keyboard,
        parse_mode: 'Markdown',
      });
    }

  } catch (error) {
    console.error('Error handling emoji game play:', error);

    // Handle specific errors with appropriate messages
    let errorKey = 'bot.errors.general';

    if (error.message && error.message.includes('not found')) {
      if (error.message.includes('submission')) {
        errorKey = 'bot.errors.submissionNotFound';
      } else {
        errorKey = 'bot.errors.contestNotFound';
      }
    } else if (error.message && error.message.includes('attempts')) {
      errorKey = 'bot.errors.noAttemptsLeft';
    } else if (error.name === 'MongoServerError') {
      errorKey = 'bot.errors.database';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorKey = 'bot.errors.network';
    }

    const translation = await getTranslation(user.language, errorKey);
    bot.sendMessage(chatId, `❌ ${translation}`);
  } finally {
    // Always remove the lock
    gamePlayLocks.delete(lockKey);
  }
};

// Telegram slot machine value mappings (1-64)
const SLOT_MACHINE_VALUES = {
  1: ["🍒", "🍒", "🍒"],    // Triple cherries
  22: ["🍇", "🍇", "🍇"],   // Triple grapes
  43: ["🍋", "🍋", "🍋"],   // Triple lemons
  64: ["7️⃣", "7️⃣", "7️⃣"],  // Triple sevens
};

// Convert Telegram slot machine value to emoji representation
const convertSlotValueToEmojis = (value) => {
  const combination = SLOT_MACHINE_VALUES[value];
  if (combination) {
    return combination.join('');
  }

  // For non-winning combinations, show a generic representation
  return "🎰";
};

// Check if slot machine value is a winning combination
const isSlotWinningCombination = (value, winningCombinations) => {
  // Telegram slot machine winning values: 1, 22, 43, 64
  const telegramWinningValues = [1, 22, 43, 64];

  // Check if the value is one of Telegram's winning values
  if (!telegramWinningValues.includes(value)) {
    return false;
  }

  // Get the emoji combination for this value
  const combination = SLOT_MACHINE_VALUES[value];
  if (!combination) {
    return false;
  }

  // Check if this combination is in the contest's winning combinations
  return winningCombinations.some(winCombo => {
    return JSON.stringify(winCombo.combination) === JSON.stringify(combination);
  });
};

// Get target information based on game type
const getTargetInfo = (gameType, contest) => {
  switch (gameType.toUpperCase()) {
    case 'DICE':
      if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
        return `Toplam ${contest.emojiGame.diceSettings.targetValue} puan`;
      } else if (contest.emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT') {
        return `En az ${contest.emojiGame.diceSettings.targetCount} adet ${contest.emojiGame.diceSettings.targetValue}`;
      }
      break;

    case 'BASKETBALL':
    case 'FOOTBALL':
      return `En az ${contest.emojiGame.successTarget} başarılı atış`;

    case 'DART':
      return `En az ${contest.emojiGame.bullseyeTarget} bullseye`;

    case 'BOWLING':
      return `En az ${contest.emojiGame.strikeTarget} strike`;

    case 'SLOT':
      const combinations = contest.emojiGame.slotSettings?.winningCombinations || [];
      if (combinations.length === 1) {
        return `${combinations[0].name} kombinasyonu`;
      } else {
        return `Herhangi bir kazanan kombinasyon`;
      }

    default:
      return 'Belirtilmemiş hedef';
  }
};

// Get appropriate score information based on game type
const getScoreInfo = (gameType, contest, submission) => {
  switch (gameType.toUpperCase()) {
    case 'DICE':
      if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
        return `Toplam Skor: ${submission.emojiResults.totalScore}`;
      } else if (contest.emojiGame.diceSettings.targetType === 'SPECIFIC_VALUE_COUNT') {
        const specificCount = submission.emojiResults.attempts.filter(a => a.result === contest.emojiGame.diceSettings.targetValue).length;
        return `${contest.emojiGame.diceSettings.targetValue} Sayısı: ${specificCount} adet`;
      }
      break;

    case 'BASKETBALL':
    case 'FOOTBALL':
      return `Başarılı Atış: ${submission.emojiResults.totalScore} adet`;

    case 'DART':
      return `Bullseye: ${submission.emojiResults.totalScore} adet`;

    case 'BOWLING':
      return `Strike: ${submission.emojiResults.totalScore} adet`;

    case 'SLOT':
      return `Kazanan Çevirme: ${submission.emojiResults.totalScore} adet`;

    default:
      return `Skor: ${submission.emojiResults.totalScore}`;
  }
};

// Send enhanced game summary
const sendGameSummary = async (bot, chatId, user, submission, gameType) => {
  try {
    // Get game type name and action term
    const gameTypeName = await getTranslation(user.language, `bot.contestDetails.gameTypes.${gameType}`) || gameType;
    const actionTerm = await getTranslation(user.language, `bot.emojiGames.actionTerms.${gameType}`) || 'Deneme';

    // Build results string
    let results = '';
    const gameEmoji = EMOJI_GAMES[gameType]?.emoji || '🎮';

    submission.emojiResults.attempts.forEach((attempt, index) => {
      let resultDisplay = '';

      switch (gameType) {
        case 'DICE':
          resultDisplay = `${attempt.result}`;
          break;
        case 'BASKETBALL':
          resultDisplay = attempt.success ? '✅ Başarılı' : '❌ Kaçırdı';
          break;
        case 'FOOTBALL':
          resultDisplay = attempt.success ? '⚽ Gol!' : '❌ Kaçırdı';
          break;
        case 'DART':
          resultDisplay = attempt.success ? '🎯 Bullseye!' : `${attempt.result} puan`;
          break;
        case 'BOWLING':
          resultDisplay = attempt.success ? '🎳 Strike!' : '❌ Kaçırdı';
          break;
        case 'SLOT':
          resultDisplay = `${attempt.result}${attempt.success ? ' 🎉' : ''}`;
          break;
        default:
          resultDisplay = attempt.result;
      }

      results += `${gameEmoji} ${actionTerm} ${index + 1}: ${resultDisplay}\n`;
    });

    // Send summary message
    const summaryMessage = await getTranslation(user.language, 'bot.emojiGames.summary', {
      gameType: gameTypeName,
      results: results.trim(),
      score: submission.emojiResults.totalScore,
    });

    await bot.sendMessage(chatId, summaryMessage, { parse_mode: 'Markdown' });

  } catch (error) {
    console.error('Error sending game summary:', error);
    // Fallback to simple summary
    const fallbackMessage = `📊 Toplam Skorunuz: ${submission.emojiResults.totalScore}\n\n📝 Katıldığınız için teşekkür ederiz!`;
    await bot.sendMessage(chatId, fallbackMessage);
  }
};

module.exports = {
  handleEmojiGameParticipation,
  startEmojiGame,
  handleEmojiGamePlay,
  sendGameInstructions,
  sendGameSummary,
  convertSlotValueToEmojis,
  isSlotWinningCombination,
  getScoreInfo,
  getTargetInfo,
  generateButtonToken,
  EMOJI_GAMES,
  SUCCESS_MESSAGES,
};
