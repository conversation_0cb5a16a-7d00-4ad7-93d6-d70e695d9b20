<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Spor Tahmin Yarı<PERSON>ı - Yönetici Paneli</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="css/custom.css" rel="stylesheet">
  <style>
    body {
      padding-top: 56px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .content {
      flex: 1;
    }

    .footer {
      margin-top: auto;
      padding: 1rem 0;
      background-color: #f8f9fa;
    }

    .login-container {
      max-width: 400px;
      margin: 2rem auto;
      padding: 2rem;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>

<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container">
      <a class="navbar-brand" href="#">Spor Tahmin Yarışması</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav" id="nav-items">
          <!-- Navigation items will be added dynamically -->
        </ul>
        <ul class="navbar-nav ms-auto" id="auth-items">
          <!-- Auth items will be added dynamically -->
        </ul>
        <div class="dropdown ms-3" id="language-selector">
          <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" id="languageDropdown"
            data-bs-toggle="dropdown" aria-expanded="false">
            <span id="current-language">Türkçe</span>
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
            <li><a class="dropdown-item" href="#" data-lang="tr">Türkçe</a></li>
            <li><a class="dropdown-item" href="#" data-lang="en">English</a></li>
            <li><a class="dropdown-item" href="#" data-lang="de">Deutsch</a></li>
            <li><a class="dropdown-item" href="#" data-lang="ar">العربية</a></li>
          </ul>
        </div>
      </div>
    </div>
  </nav>

  <div class="container content mt-4" id="app">
    <!-- Content will be loaded here -->
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>

  <footer class="footer">
    <div class="container">
      <div class="text-center">
        <p class="mb-0">&copy; 2025 Spor Tahmin Yarışması. Tüm hakları saklıdır.</p>
      </div>
    </div>
  </footer>

  <!-- Bildirim Modalları -->
  <div id="notificationModals"></div>

  <!-- Dinamik Modal -->
  <div class="modal fade" id="dynamicModal" tabindex="-1" aria-labelledby="dynamicModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="dynamicModalTitle"></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="dynamicModalBody">
          <!-- İçerik dinamik olarak yüklenecek -->
        </div>
        <div class="modal-footer" id="dynamicModalFooter">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
            data-i18n="app.common.close">Kapat</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/i18next@21.6.10/i18next.min.js"></script>
  <script
    src="https://cdn.jsdelivr.net/npm/i18next-browser-languagedetector@6.1.3/i18nextBrowserLanguageDetector.min.js"></script>
  <script src="js/i18n-config.js"></script>
  <script src="js/admins.js"></script>
  <script src="js/notifications.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
  <script>
    // Wait for i18n to be ready before loading app.js
    document.addEventListener('DOMContentLoaded', () => {
      // Create a script element for app.js
      const appScript = document.createElement('script');
      appScript.src = 'js/app.js';
      appScript.onload = () => {
        console.log('app.js loaded, calling init()');
        // Call init function when app.js is loaded
        if (typeof init === 'function') {
          init();
        }
      };
      // Append the script to the document
      document.body.appendChild(appScript);
    });
  </script>
</body>

</html>