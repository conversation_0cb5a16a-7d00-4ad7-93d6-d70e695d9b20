const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Create test users
const createTestUsers = async () => {
  try {
    // Check if users already exist
    const userCount = await User.countDocuments();
    
    if (userCount > 0) {
      console.log(`${userCount} users already exist. Skipping test user creation.`);
      return;
    }
    
    // Create test users
    const testUsers = [
      {
        telegramId: '1001',
        username: 'testuser1',
        firstName: 'Test',
        lastName: 'User1',
        language: 'tr'
      },
      {
        telegramId: '1002',
        username: 'testuser2',
        firstName: 'Test',
        lastName: 'User2',
        language: 'en'
      },
      {
        telegramId: '1003',
        username: 'testuser3',
        firstName: 'Test',
        lastName: 'User3',
        language: 'tr'
      },
      {
        telegramId: '1004',
        username: 'testuser4',
        firstName: 'Test',
        lastName: 'User4',
        language: 'de'
      },
      {
        telegramId: '1005',
        username: 'testuser5',
        firstName: 'Test',
        lastName: 'User5',
        language: 'ar'
      }
    ];
    
    // Insert users
    const result = await User.insertMany(testUsers);
    console.log(`${result.length} test users created successfully`);
  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    // Close connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
};

// Run the function
createTestUsers();
