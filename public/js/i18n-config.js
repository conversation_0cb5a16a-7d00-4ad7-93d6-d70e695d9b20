// i18n configuration
let translationsLoaded = false;
let initialUIUpdatePending = true;

// Initialize i18next
i18next
  .use(i18nextBrowserLanguageDetector)
  .init({
    fallbackLng: 'tr',
    debug: false,
    load: 'languageOnly',
    detection: {
      order: ['localStorage', 'navigator'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },
    interpolation: {
      escapeValue: false
    }
  });

// Load translations from server
async function loadTranslations(lang) {
  try {
    console.log(`Loading translations for language: ${lang}`);
    const response = await fetch(`/locales/${lang}/translation.json`);
    if (!response.ok) {
      throw new Error(`Failed to load translations: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Translations loaded successfully');

    // Update resources
    i18next.addResourceBundle(lang, 'translation', data);

    // Set language
    await i18next.changeLanguage(lang);

    // Mark translations as loaded
    translationsLoaded = true;

    // Update UI
    updateUI();

    // Store language preference
    localStorage.setItem('i18nextLng', lang);

    return true;
  } catch (error) {
    console.error('Error loading translations:', error);
    return false;
  }
}

// Update UI based on current language
function updateUI() {
  if (!translationsLoaded && !initialUIUpdatePending) {
    console.log('Skipping UI update - translations not loaded yet');
    return;
  }

  console.log('Updating UI with translations');
  initialUIUpdatePending = false;

  // Update current language display
  const currentLang = i18next.language;
  const langNames = {
    tr: 'Türkçe',
    en: 'English',
    de: 'Deutsch',
    ar: 'العربية'
  };

  const langElement = document.getElementById('current-language');
  if (langElement) {
    langElement.textContent = langNames[currentLang] || 'Türkçe';
  }

  // Update document direction for Arabic
  document.documentElement.dir = currentLang === 'ar' ? 'rtl' : 'ltr';

  // Update all elements with data-i18n attribute
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    if (key && i18next.exists(key)) {
      element.textContent = i18next.t(key);
    }
  });

  // Update welcome message in navigation
  const welcomeElement = document.querySelector('.welcome-message');
  if (welcomeElement && window.state && window.state.admin) {
    welcomeElement.textContent = `${i18next.t('app.common.welcome')}, ${window.state.admin.name}`;
  }

  // Update page title
  if (i18next.exists('app.title') && i18next.exists('app.login.title')) {
    document.title = i18next.t('app.title') + ' - ' + i18next.t('app.login.title');
  }

  console.log('UI updated successfully');
}

// Add event listeners to language selector
document.addEventListener('DOMContentLoaded', async () => {
  console.log('DOM loaded, initializing translations');

  // Load current language
  const currentLang = localStorage.getItem('i18nextLng') || 'tr';
  console.log(`Current language from localStorage: ${currentLang}`);

  // First attempt to load translations
  const success = await loadTranslations(currentLang);

  // If failed, try with default language
  if (!success && currentLang !== 'tr') {
    console.log('Falling back to default language (tr)');
    await loadTranslations('tr');
  }

  // Add event listeners to language selector
  const langItems = document.querySelectorAll('#language-selector .dropdown-item');
  console.log(`Found ${langItems.length} language selector items`);

  langItems.forEach(item => {
    item.addEventListener('click', async (e) => {
      e.preventDefault();
      const lang = e.target.getAttribute('data-lang');
      console.log(`Language selected: ${lang}`);
      await loadTranslations(lang);
    });
  });

  // Force UI update if translations are loaded
  if (translationsLoaded) {
    console.log('Forcing initial UI update');
    updateUI();
  }
});

// Add a listener for i18next initialized event
i18next.on('initialized', function () {
  console.log('i18next initialized');
});

// Add a listener for language changed event
i18next.on('languageChanged', function (lng) {
  console.log(`Language changed to: ${lng}`);
  updateUI();
});
