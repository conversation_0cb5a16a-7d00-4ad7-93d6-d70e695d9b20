const BotSettings = require('../models/BotSettings');

// @desc    Get bot settings
// @route   GET /api/bot-settings
// @access  Private/Admin
exports.getBotSettings = async (req, res) => {
  try {
    const settings = await BotSettings.getSettings();

    res.status(200).json({
      success: true,
      settings,
    });
  } catch (error) {
    console.error('Get bot settings error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update bot settings
// @route   PUT /api/bot-settings
// @access  Private/Admin
exports.updateBotSettings = async (req, res) => {
  try {
    const {
      welcomeMessage,
      channelRequirement,
      botUsername,
      isActive,
      maintenanceMode,
      maintenanceMessage,
      notifications,
    } = req.body;

    let settings = await BotSettings.getSettings();

    // Update settings
    settings.welcomeMessage = welcomeMessage || settings.welcomeMessage;
    settings.channelRequirement = channelRequirement || settings.channelRequirement;
    settings.botUsername = botUsername || settings.botUsername;

    if (typeof isActive !== 'undefined') {
      settings.isActive = isActive;
    }

    if (typeof maintenanceMode !== 'undefined') {
      settings.maintenanceMode = maintenanceMode;
    }

    settings.maintenanceMessage = maintenanceMessage || settings.maintenanceMessage;

    // Update notification settings if provided
    if (notifications) {
      // Check if we should update dailyLimit
      if (notifications.dailyLimit !== undefined) {
        const dailyLimit = parseInt(notifications.dailyLimit, 10);
        if (!isNaN(dailyLimit) && dailyLimit >= 0 && dailyLimit <= 10000) {
          settings.notifications.dailyLimit = dailyLimit;

          // Also update .env file if we have access
          try {
            const fs = require('fs');
            const path = require('path');
            const dotenv = require('dotenv');

            // .env dosyasının yolunu belirle
            const envPath = path.resolve(process.cwd(), '.env');

            // .env dosyasını oku
            let envContent = fs.readFileSync(envPath, 'utf8');

            // NOTIFICATION_DAILY_LIMIT değerini güncelle veya ekle
            if (envContent.includes('NOTIFICATION_DAILY_LIMIT=')) {
              envContent = envContent.replace(
                /NOTIFICATION_DAILY_LIMIT=.*/,
                `NOTIFICATION_DAILY_LIMIT=${dailyLimit}`
              );
            } else {
              envContent += `\nNOTIFICATION_DAILY_LIMIT=${dailyLimit}\n`;
            }

            // Güncellenmiş içeriği .env dosyasına yaz
            fs.writeFileSync(envPath, envContent);

            // Güncellenmiş .env dosyasını yükle
            dotenv.config();

            console.log(`NOTIFICATION_DAILY_LIMIT updated in .env file: ${dailyLimit}`);
          } catch (err) {
            console.error('Error updating .env file:', err);
            // .env güncellenemese bile devam et
          }
        }
      }

      // Update other notification settings
      if (notifications.batchSize !== undefined) {
        const batchSize = parseInt(notifications.batchSize, 10);
        if (!isNaN(batchSize) && batchSize >= 10) {
          settings.notifications.batchSize = batchSize;
        }
      }

      if (notifications.delayBetweenBatches !== undefined) {
        const delay = parseInt(notifications.delayBetweenBatches, 10);
        if (!isNaN(delay) && delay >= 500) {
          settings.notifications.delayBetweenBatches = delay;
        }
      }

      if (notifications.maxRecipientsPerNotification !== undefined) {
        const maxRecipients = parseInt(notifications.maxRecipientsPerNotification, 10);
        if (!isNaN(maxRecipients) && maxRecipients >= 100) {
          settings.notifications.maxRecipientsPerNotification = maxRecipients;
        }
      }
    }

    await settings.save();

    res.status(200).json({
      success: true,
      settings,
    });
  } catch (error) {
    console.error('Update bot settings error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
