const jwt = require('jsonwebtoken');
const Admin = require('../models/Admin');

module.exports = async (req, res, next) => {
  try {
    // Token'ı al
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Yetkilendirme hatası: Token bulunamadı' });
    }
    
    // Token'ı doğrula
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Admin'i bul
    const admin = await Admin.findById(decoded.id);
    
    if (!admin) {
      return res.status(401).json({ message: 'Yetkilendirme hatası: Geçersiz token' });
    }
    
    // Admin aktif değilse
    if (!admin.isActive) {
      return res.status(401).json({ message: 'Hesabınız devre dışı bırakılmış' });
    }
    
    // Admin bilgilerini request'e ekle
    req.user = {
      id: admin._id,
      username: admin.username,
      email: admin.email,
      role: admin.role
    };
    
    next();
  } catch (error) {
    console.error('Auth middleware hatası:', error);
    res.status(401).json({ message: 'Yetkilendirme hatası: ' + error.message });
  }
};
