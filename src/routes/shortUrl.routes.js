const express = require('express');
const router = express.Router();
const shortUrlService = require('../services/shortUrlService');
const User = require('../models/User');
const Notification = require('../models/Notification');

// Kısa URL yönlendirme
router.get('/:shortCode', async (req, res) => {
  try {
    const { shortCode } = req.params;
    const { uid } = req.query; // Telegram kullanıcı ID'si

    let userId = null;
    if (uid) {
      const user = await User.findOne({ telegramId: uid });
      if (user) {
        userId = user._id;
      }
    }

    const originalUrl = await shortUrlService.redirectShortUrl(shortCode, userId);
    res.redirect(originalUrl);
  } catch (error) {
    console.error('URL yönlendirme hatası:', error);
    res.status(404).send('Sayfa bulunamadı');
  }
});



module.exports = router;
