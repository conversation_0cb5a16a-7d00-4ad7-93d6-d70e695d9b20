const Contest = require('../../models/Contest');
const Submission = require('../../models/Submission');
const { sendQuestion } = require('./contestParticipation');
const { getTranslation } = require('../translations');

// <PERSON><PERSON> answers confirmation
const handleConfirmation = async (bot, callbackQuery, user, confirmed) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Check if user is in confirming state
    if (user.state !== 'CONFIRMING') {
      const translation = await getTranslation(user.language, 'bot.confirmation.noActiveSubmission');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    // Get contest
    const contest = await Contest.findById(user.currentContest);

    if (!contest || contest.status !== 'ACTIVE') {
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentQuestionIndex = 0;
      user.tempAnswers = new Map();
      await user.save();
      const translation = await getTranslation(user.language, 'bot.confirmation.contestNotActive');
      return bot.sendMessage(
        chatId,
        translation
      );
    }

    if (confirmed) {
      // Create submission
      await Submission.create({
        user: user._id,
        contest: user.currentContest,
        answers: user.tempAnswers,
        submittedAt: new Date(),
      });

      // Reset user state
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentQuestionIndex = 0;
      user.tempAnswers = new Map();
      await user.save();

      // Edit the original message to remove buttons
      bot.editMessageReplyMarkup(
        { inline_keyboard: [] },
        {
          chat_id: chatId,
          message_id: callbackQuery.message.message_id,
        }
      );

      const translation = await getTranslation(user.language, 'bot.confirmation.success', { title: contest.title });

      // Send confirmation message
      bot.sendMessage(
        chatId,
        translation
      );
    } else {
      // User wants to review answers
      user.state = 'ANSWERING';
      user.currentQuestionIndex = 0;
      await user.save();

      // Edit the original message to remove buttons
      bot.editMessageReplyMarkup(
        { inline_keyboard: [] },
        {
          chat_id: chatId,
          message_id: callbackQuery.message.message_id,
        }
      );

      const translation = await getTranslation(user.language, 'bot.confirmation.review');

      // Send message
      bot.sendMessage(
        chatId,
        translation
      );

      // Send first question
      await sendQuestion(bot, chatId, user, contest);
    }
  } catch (error) {
    console.error('Error handling confirmation:', error);
    const translation = await getTranslation(user.language, 'bot.errors.general');
    // Send error message
    bot.sendMessage(
      chatId,
      translation
    );
  }
};

module.exports = { handleConfirmation };
