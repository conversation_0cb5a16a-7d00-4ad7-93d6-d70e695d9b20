const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const notificationService = require('../services/notificationService');
const shortUrlService = require('../services/shortUrlService');
const authMiddleware = require('../middleware/auth');
const Admin = require('../models/Admin');
const Notification = require('../models/Notification');

// Multer ayarları
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: (req, file, cb) => {
    // Sadece belirli dosya türlerine izin ver
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('<PERSON><PERSON><PERSON>, PNG ve GIF formatları desteklenmektedir.'), false);
    }
  }
});

// Yetki kontrolü middleware
const checkNotificationPermission = async (req, res, next) => {
  try {
    const admin = await Admin.findById(req.user.id);

    if (!admin) {
      return res.status(404).json({ message: 'Yönetici bulunamadı' });
    }

    if (admin.role === 'superadmin' || admin.permissions.sendNotifications) {
      next();
    } else {
      res.status(403).json({ message: 'Bildirim gönderme yetkiniz bulunmamaktadır' });
    }
  } catch (error) {
    console.error('Yetki kontrolü hatası:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// Bildirimleri listele
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const filters = {};

    if (status) {
      filters.status = status;
    }

    // Süper admin değilse sadece kendi bildirimlerini görebilir
    const admin = await Admin.findById(req.user.id);
    if (admin.role !== 'superadmin') {
      filters.createdBy = req.user.id;
    }

    const result = await notificationService.listNotifications(filters, page, limit);
    res.json(result);
  } catch (error) {
    console.error('Bildirimleri listeleme hatası:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
});

// Bildirim detaylarını getir
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const notification = await notificationService.getNotificationDetails(req.params.id);

    // Süper admin değilse sadece kendi bildirimlerini görebilir
    const admin = await Admin.findById(req.user.id);
    if (admin.role !== 'superadmin' && notification.createdBy._id.toString() !== req.user.id) {
      return res.status(403).json({ message: 'Bu bildirime erişim yetkiniz bulunmamaktadır' });
    }

    // retryCount değerini kontrol et ve varsayılan değer ata
    if (notification.retryCount === undefined) {
      notification.retryCount = 0;
    }

    // Yanıtı düzenle
    res.json({
      notification: notification
    });
  } catch (error) {
    console.error('Bildirim detayları getirme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Yeni bildirim oluştur
router.post('/', authMiddleware, checkNotificationPermission, upload.single('image'), async (req, res) => {
  try {
    console.log("Gelen request body:", req.body); // Debug için

    const { title, message } = req.body;
    let urls = [];
    let recipients = [];

    // URL'leri işle
    if (req.body.urls) {
      // Eğer urls bir dizi ise
      if (Array.isArray(req.body.urls)) {
        urls = req.body.urls;
      }
      // Eğer urls bir string ise ve JSON formatında ise
      else if (typeof req.body.urls === 'string' && req.body.urls.startsWith('[')) {
        try {
          urls = JSON.parse(req.body.urls);
        } catch (e) {
          urls = [req.body.urls];
        }
      }
      // Eğer urls bir string ise
      else if (typeof req.body.urls === 'string') {
        urls = [req.body.urls];
      }
      // Eğer urls bir obje ise (formData ile gönderildiğinde olabilir)
      else if (typeof req.body.urls === 'object') {
        urls = Object.values(req.body.urls);
      }
    }

    // Alıcıları işle
    if (req.body.recipients) {
      // Eğer recipients bir dizi ise
      if (Array.isArray(req.body.recipients)) {
        recipients = req.body.recipients;
      }
      // Eğer recipients bir string ise ve JSON formatında ise
      else if (typeof req.body.recipients === 'string' && req.body.recipients.startsWith('[')) {
        try {
          recipients = JSON.parse(req.body.recipients);
        } catch (e) {
          recipients = [];
        }
      }
      // Eğer recipients bir obje ise (formData ile gönderildiğinde olabilir)
      else if (typeof req.body.recipients === 'object') {
        recipients = Object.values(req.body.recipients);
      }
    }

    // Butonları işle
    let buttons = [];
    if (req.body.buttons) {
      // Eğer buttons bir dizi ise
      if (Array.isArray(req.body.buttons)) {
        buttons = req.body.buttons;
      }
      // Eğer buttons bir string ise ve JSON formatında ise
      else if (typeof req.body.buttons === 'string' && req.body.buttons.startsWith('[')) {
        try {
          buttons = JSON.parse(req.body.buttons);
        } catch (e) {
          buttons = [];
        }
      }
      // Eğer buttons bir obje ise (formData ile gönderildiğinde olabilir)
      else if (typeof req.body.buttons === 'object') {
        // Butonları text ve url çiftleri olarak işle
        const buttonTexts = req.body.buttonTexts || {};
        const buttonUrls = req.body.buttonUrls || {};

        // Buton sayısını belirle
        const buttonCount = Math.max(
          Object.keys(buttonTexts).length,
          Object.keys(buttonUrls).length
        );

        // Butonları oluştur
        for (let i = 0; i < buttonCount; i++) {
          const text = buttonTexts[i] || `Button ${i + 1}`;
          const url = buttonUrls[i] || '';

          if (text && url) {
            buttons.push({ text, url });
          }
        }
      }
    }

    if (!title || !message) {
      return res.status(400).json({ message: 'Başlık ve mesaj alanları zorunludur' });
    }

    // Günlük limit kontrolü
    const limitCheck = await notificationService.checkDailyLimit();
    if (!limitCheck.canSend) {
      return res.status(400).json({
        message: 'Günlük bildirim gönderme limitine ulaşıldı',
        limit: limitCheck
      });
    }

    // Fotoğraf kontrolü
    const imageFile = req.file || null;

    const notification = await notificationService.createNotification(
      { title, message, urls, buttons, recipients },
      req.user.id,
      imageFile
    );

    // Eğer bildirim SENDING durumunda oluşturulduysa (belirli alıcılar belirtilmişse) otomatik olarak göndermeye başla
    if (notification.status === 'SENDING') {
      // Bildirim gönderme işlemini başlat
      notificationService.sendNotification(notification._id)
        .catch(error => {
          console.error('Otomatik bildirim gönderme hatası:', error);
        });
    }

    res.status(201).json(notification);
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim önizleme
router.get('/:id/preview', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const preview = await notificationService.previewNotification(req.params.id);
    res.json(preview);
  } catch (error) {
    console.error('Bildirim önizleme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim gönder
router.post('/:id/send', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    // Günlük limit kontrolü
    const limitCheck = await notificationService.checkDailyLimit();
    if (!limitCheck.canSend) {
      return res.status(400).json({
        message: 'Günlük bildirim gönderme limitine ulaşıldı',
        limit: limitCheck
      });
    }

    const notification = await notificationService.sendNotification(req.params.id);
    res.json({ message: 'Bildirim gönderimi başlatıldı', notification });
  } catch (error) {
    console.error('Bildirim gönderme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim gönderimini duraklat
router.post('/:id/pause', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await notificationService.pauseNotification(req.params.id);
    res.json({ message: 'Bildirim gönderimi duraklatıldı', notification });
  } catch (error) {
    console.error('Bildirim duraklatma hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim gönderimini devam ettir
router.post('/:id/resume', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await notificationService.resumeNotification(req.params.id);
    res.json({ message: 'Bildirim gönderimi devam ettiriliyor', notification });
  } catch (error) {
    console.error('Bildirim devam ettirme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim gönderimini durdur
router.post('/:id/stop', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await notificationService.stopNotification(req.params.id);
    res.json({ message: 'Bildirim gönderimi durduruldu', notification });
  } catch (error) {
    console.error('Bildirim durdurma hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Başarısız bildirimleri yeniden gönder
router.post('/:id/retry', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await notificationService.retryFailedNotification(req.params.id);
    res.json({ message: 'Başarısız bildirimler yeniden gönderiliyor', notification });
  } catch (error) {
    console.error('Başarısız bildirimleri yeniden gönderme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Tek bir alıcıya bildirimi yeniden gönder
router.post('/:id/retry-single', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ message: 'Kullanıcı ID gerekli' });
    }

    const notification = await notificationService.retrySingleRecipient(req.params.id, userId);
    res.json({ message: 'Bildirim alıcıya yeniden gönderiliyor', notification });
  } catch (error) {
    console.error('Tek alıcıya bildirim yeniden gönderme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Başarısız bildirimi yeniden gönder (FAILED durumundaki bildirimler için)
router.post('/:id/resend', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await notificationService.resendNotification(req.params.id);
    res.json({ message: 'Bildirim yeniden gönderiliyor', notification });
  } catch (error) {
    console.error('Bildirimi yeniden gönderme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Günlük bildirim limitini kontrol et
router.get('/limits/daily', authMiddleware, async (req, res) => {
  try {
    const limitCheck = await notificationService.checkDailyLimit();
    res.json(limitCheck);
  } catch (error) {
    console.error('Limit kontrol hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Kısa URL istatistiklerini getir
router.get('/url/:shortCode/stats', authMiddleware, async (req, res) => {
  try {
    const stats = await shortUrlService.getShortUrlStats(req.params.shortCode);
    res.json(stats);
  } catch (error) {
    console.error('URL istatistikleri getirme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim raporu
router.get('/:id/report', authMiddleware, checkNotificationPermission, async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id)
      .populate({
        path: 'recipients.user',
        select: 'username telegramId'
      });

    if (!notification) {
      return res.status(404).json({ message: 'Bildirim bulunamadı' });
    }

    // Gönderim detaylarını hazırla
    const deliveries = notification.recipients.map(recipient => {
      let status = 'PENDING';
      if (recipient.status === 'SENT') {
        status = 'SUCCESS';
      } else if (recipient.status === 'FAILED') {
        status = 'FAILED';
      } else if (recipient.status === 'RETRY') {
        status = 'RETRY';
      }

      console.log(`Kullanıcı: ${recipient.user?.username || 'Bilinmeyen'}, Durum: ${recipient.status} -> ${status}`);

      return {
        user: recipient.user,
        status,
        sentAt: recipient.sentAt,
        error: recipient.errorMessage,
        retryCount: recipient.retryCount || 0,
        lastRetryAt: recipient.lastRetryAt
      };
    });

    // Sayaçları manuel olarak hesapla
    let successCount = 0;
    let failedCount = 0;
    let pendingCount = 0;
    let retryCount = 0;

    deliveries.forEach(delivery => {
      if (delivery.status === 'SUCCESS') {
        successCount++;
      } else if (delivery.status === 'FAILED') {
        failedCount++;
      } else if (delivery.status === 'RETRY') {
        retryCount++;
      } else {
        pendingCount++;
      }
    });

    console.log(`Toplam: ${deliveries.length}, Başarılı: ${successCount}, Başarısız: ${failedCount}, Bekleyen: ${pendingCount}, Yeniden Deneme: ${retryCount}`);

    // Toplam bekleyen sayısı = bekleyen + yeniden deneme
    const totalPending = pendingCount + retryCount;

    res.json({
      notification: {
        id: notification._id,
        title: notification.title,
        status: notification.status,
        totalRecipients: deliveries.length,
        successCount: successCount,
        failedCount: failedCount,
        pendingCount: totalPending,
        startedAt: notification.startedAt,
        completedAt: notification.completedAt,
        retryCount: notification.retryCount || 0
      },
      deliveries
    });
  } catch (error) {
    console.error('Bildirim raporu hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

// Bildirim fotoğrafını getir
router.get('/:id/image', async (req, res) => {
  try {
    const notification = await notificationService.getNotificationDetails(req.params.id);

    if (!notification || !notification.image) {
      return res.status(404).json({ message: 'Fotoğraf bulunamadı' });
    }

    console.log("Fotoğraf yolu:", notification.image);

    // Önce normal yolu dene
    let imagePath = path.join(process.cwd(), notification.image);
    console.log("Tam dosya yolu:", imagePath);

    // Dosya yoksa alternatif yolu dene (başında / olmadan)
    if (!fs.existsSync(imagePath) && notification.image.startsWith('/')) {
      const alternativePath = path.join(process.cwd(), notification.image.replace(/^\//, ''));
      console.log("Alternatif dosya yolu:", alternativePath);

      if (fs.existsSync(alternativePath)) {
        imagePath = alternativePath;
      }
    }

    // Hala dosya bulunamadıysa
    if (!fs.existsSync(imagePath)) {
      console.error("Fotoğraf dosyası bulunamadı:", imagePath);
      return res.status(404).json({ message: 'Fotoğraf dosyası bulunamadı' });
    }

    console.log("Fotoğraf gönderiliyor:", imagePath);
    res.sendFile(imagePath);
  } catch (error) {
    console.error('Fotoğraf getirme hatası:', error);
    res.status(500).json({ message: error.message || 'Sunucu hatası' });
  }
});

module.exports = router;
