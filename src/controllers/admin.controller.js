const Admin = require('../models/Admin');
const bcrypt = require('bcrypt');

// @desc    Get all admins
// @route   GET /api/admins
// @access  Private/SuperAdmin
exports.getAdmins = async (req, res) => {
  try {
    // Only superadmin can see all admins
    if (req.admin.role !== 'superadmin') {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    const admins = await Admin.find().select('-password').sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: admins.length,
      admins,
    });
  } catch (error) {
    console.error('Get admins error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Get single admin
// @route   GET /api/admins/:id
// @access  Private/SuperAdmin
exports.getAdmin = async (req, res) => {
  try {
    // Only superadmin can see other admins
    if (req.admin.role !== 'superadmin' && req.admin.id !== req.params.id) {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    const admin = await Admin.findById(req.params.id).select('-password');

    if (!admin) {
      return res.status(404).json({ message: 'Yönetici bulunamadı' });
    }

    res.status(200).json({
      success: true,
      admin,
    });
  } catch (error) {
    console.error('Get admin error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Create new admin
// @route   POST /api/admins
// @access  Private/SuperAdmin
exports.createAdmin = async (req, res) => {
  try {
    // Only superadmin can create admins
    if (req.admin.role !== 'superadmin') {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    const { username, password, email, name, role } = req.body;

    // Check if admin already exists
    const adminExists = await Admin.findOne({ $or: [{ username }, { email }] });
    if (adminExists) {
      return res.status(400).json({ message: 'Bu kullanıcı adı veya e-posta zaten kullanılıyor' });
    }

    // Create new admin
    const admin = await Admin.create({
      username,
      password,
      email,
      name,
      role: role || 'admin',
    });

    res.status(201).json({
      success: true,
      message: 'Yönetici başarıyla oluşturuldu',
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        name: admin.name,
        role: admin.role,
      },
    });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Update admin
// @route   PUT /api/admins/:id
// @access  Private/SuperAdmin or Self
exports.updateAdmin = async (req, res) => {
  try {
    const { username, email, name, role, isActive } = req.body;

    // Check if admin exists
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      return res.status(404).json({ message: 'Yönetici bulunamadı' });
    }

    // Only superadmin can update role or other admins
    if (req.admin.role !== 'superadmin' && (req.admin.id !== req.params.id || role)) {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    // Update admin
    if (username) admin.username = username;
    if (email) admin.email = email;
    if (name) admin.name = name;
    
    // Only superadmin can update role and active status
    if (req.admin.role === 'superadmin') {
      if (role) admin.role = role;
      if (typeof isActive === 'boolean') admin.isActive = isActive;
    }

    await admin.save();

    res.status(200).json({
      success: true,
      message: 'Yönetici başarıyla güncellendi',
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        isActive: admin.isActive,
      },
    });
  } catch (error) {
    console.error('Update admin error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Delete admin
// @route   DELETE /api/admins/:id
// @access  Private/SuperAdmin
exports.deleteAdmin = async (req, res) => {
  try {
    // Only superadmin can delete admins
    if (req.admin.role !== 'superadmin') {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    // Prevent superadmin from deleting themselves
    if (req.admin.id === req.params.id) {
      return res.status(400).json({
        message: 'Kendinizi silemezsiniz',
      });
    }

    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      return res.status(404).json({ message: 'Yönetici bulunamadı' });
    }

    await admin.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Yönetici başarıyla silindi',
    });
  } catch (error) {
    console.error('Delete admin error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};

// @desc    Change admin password
// @route   PUT /api/admins/:id/change-password
// @access  Private/SuperAdmin or Self
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Check if admin exists
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      return res.status(404).json({ message: 'Yönetici bulunamadı' });
    }

    // Only superadmin can change other admins' passwords
    // Regular admins can only change their own password
    if (req.admin.role !== 'superadmin' && req.admin.id !== req.params.id) {
      return res.status(403).json({
        message: 'Bu işlem için yetkiniz bulunmamaktadır',
      });
    }

    // If not superadmin, verify current password
    if (req.admin.role !== 'superadmin' || req.admin.id === req.params.id) {
      const isMatch = await admin.matchPassword(currentPassword);
      if (!isMatch) {
        return res.status(401).json({ message: 'Mevcut şifre yanlış' });
      }
    }

    // Update password
    admin.password = newPassword;
    await admin.save();

    res.status(200).json({
      success: true,
      message: 'Şifre başarıyla değiştirildi',
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ message: 'Sunucu hatası' });
  }
};
