const express = require('express');
const router = express.Router();
const {
  login,
  register,
  getProfile,
  updatePassword,
} = require('../controllers/auth.controller');
const { protect, authorize } = require('../middleware/auth.middleware');

// Public routes
router.post('/login', login);

// Protected routes
router.get('/profile', protect, getProfile);
router.put('/update-password', protect, updatePassword);

// SuperAdmin only routes
router.post('/register', protect, authorize('superadmin'), register);

module.exports = router;
