const { getTranslation } = require('../translations');

const handleRegistration = async (bot, callbackQuery, user, isConfirmed) => {
  try {
    const chatId = callbackQuery.message.chat.id;

    if (isConfirmed) {
      // Username confirmed - update username and change state
      if (user.tempUsername) {
        user.username = user.tempUsername;
        user.tempUsername = undefined; // Clear temporary username
      }
      user.state = 'IDLE';
      await user.save();

      // Get registration complete message
      const completeMessage = await getTranslation(user.language, 'bot.registration.complete', { username: user.username });

      bot.sendMessage(
        chatId,
        completeMessage
      );

      // Edit the original message to remove buttons
      bot.editMessageReplyMarkup(
        { inline_keyboard: [] },
        {
          chat_id: chatId,
          message_id: callbackQuery.message.message_id,
        }
      );
    } else {
      // Username rejected, ask again
      user.tempUsername = undefined; // Clear temporary username
      user.state = 'REGISTERING';
      await user.save();

      // Get ask again message
      const askAgainMessage = await getTranslation(user.language, 'bot.registration.askAgain');

      bot.sendMessage(
        chatId,
        askAgainMessage
      );

      // Edit the original message to remove buttons
      bot.editMessageReplyMarkup(
        { inline_keyboard: [] },
        {
          chat_id: chatId,
          message_id: callbackQuery.message.message_id,
        }
      );
    }
  } catch (error) {
    console.error('Error handling registration:', error);
    // Get error message
    const errorMessage = await getTranslation(user.language, 'bot.errors.general');

    bot.sendMessage(
      chatId,
      errorMessage
    );
  }
};

module.exports = { handleRegistration };
