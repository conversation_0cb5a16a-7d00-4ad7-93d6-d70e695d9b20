// Auth JS

// Check if user is logged in
function checkAuth() {
  const token = localStorage.getItem('token');
  if (!token) {
    // If not on login page, redirect to login
    if (!window.location.pathname.includes('login.html')) {
      window.location.href = 'login.html';
    }
    return false;
  }
  
  // If on login page and already logged in, redirect to dashboard
  if (window.location.pathname.includes('login.html')) {
    window.location.href = 'dashboard.html';
  }
  
  return true;
}

// Show alert message
function showAlert(type, message) {
  const alertContainer = document.getElementById('alertContainer');
  if (!alertContainer) return;
  
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  `;
  
  alertContainer.innerHTML = '';
  alertContainer.appendChild(alertDiv);
  
  // Auto dismiss after 5 seconds
  setTimeout(() => {
    alertDiv.classList.remove('show');
    setTimeout(() => {
      alertDiv.remove();
    }, 300);
  }, 5000);
}

// Login form submission
document.addEventListener('DOMContentLoaded', function() {
  const loginForm = document.getElementById('loginForm');
  
  if (loginForm) {
    loginForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      
      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.message || 'Giriş başarısız');
        }
        
        // Save token and admin info
        localStorage.setItem('token', data.token);
        localStorage.setItem('admin', JSON.stringify(data.admin));
        
        // Redirect to dashboard
        window.location.href = 'dashboard.html';
      } catch (error) {
        showAlert('danger', error.message);
      }
    });
  }
  
  // Logout button
  const logoutBtn = document.getElementById('logoutBtn');
  if (logoutBtn) {
    logoutBtn.addEventListener('click', function(e) {
      e.preventDefault();
      
      // Clear local storage
      localStorage.removeItem('token');
      localStorage.removeItem('admin');
      
      // Redirect to login
      window.location.href = 'login.html';
    });
  }
  
  // Check auth on page load (except for login page)
  if (!window.location.pathname.includes('login.html')) {
    checkAuth();
  } else {
    // If on login page, check if already logged in
    const token = localStorage.getItem('token');
    if (token) {
      window.location.href = 'dashboard.html';
    }
  }
});

// Get admin info
function getAdminInfo() {
  const adminStr = localStorage.getItem('admin');
  if (!adminStr) return null;
  
  try {
    return JSON.parse(adminStr);
  } catch (error) {
    console.error('Error parsing admin info:', error);
    return null;
  }
}

// Check if admin has permission
function hasPermission(permission) {
  const admin = getAdminInfo();
  if (!admin) return false;
  
  // Superadmin has all permissions
  if (admin.role === 'superadmin') return true;
  
  // Check specific permission
  return admin.permissions && admin.permissions[permission];
}
