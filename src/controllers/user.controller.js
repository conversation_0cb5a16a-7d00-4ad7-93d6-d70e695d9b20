const User = require('../models/User');
const Submission = require('../models/Submission');
const { validationResult } = require('express-validator');

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
exports.getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', state = '', language = '' } = req.query;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};

    // Add search filter if provided
    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { telegramId: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    // Add state filter if provided
    if (state) {
      filter.state = state;
    }

    // Add language filter if provided
    if (language) {
      filter.language = language;
    }

    // Get users with pagination
    const users = await User.find(filter)
      .populate('blockedBy', 'name username')
      .sort({ _id: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await User.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: users.length,
      users,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .populate('blockedBy', 'name username');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get user's submissions
    const submissions = await Submission.find({ user: req.params.id })
      .populate('contest', 'title status')
      .sort({ submittedAt: -1 });

    res.status(200).json({
      success: true,
      user,
      submissions,
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Block user
// @route   PUT /api/users/:id/block
// @access  Private/Admin
exports.blockUser = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { reason } = req.body;

    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.isAdmin) {
      return res.status(400).json({ message: 'Cannot block admin users' });
    }

    user.isBlocked = true;
    user.blockReason = reason || '';
    user.blockedBy = req.admin._id;
    user.blockedAt = new Date();

    await user.save();

    res.status(200).json({
      success: true,
      message: 'User blocked successfully',
      user
    });
  } catch (error) {
    console.error('Block user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Unblock user
// @route   PUT /api/users/:id/unblock
// @access  Private/Admin
exports.unblockUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.isBlocked = false;
    user.blockReason = '';
    user.blockedBy = null;
    user.blockedAt = null;

    await user.save();

    res.status(200).json({
      success: true,
      message: 'User unblocked successfully',
      user
    });
  } catch (error) {
    console.error('Unblock user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res) => {
  try {
    const { username, isAdmin } = req.body;

    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user
    user.username = username || user.username;

    if (typeof isAdmin !== 'undefined') {
      user.isAdmin = isAdmin;
    }

    await user.save();

    res.status(200).json({
      success: true,
      user,
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user has submissions
    const submissionsCount = await Submission.countDocuments({
      user: req.params.id,
    });

    if (submissionsCount > 0) {
      return res.status(400).json({
        message: 'Cannot delete user with submissions',
      });
    }

    await user.deleteOne();

    res.status(200).json({
      success: true,
      message: 'User deleted',
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get user statistics
// @route   GET /api/users/stats
// @access  Private/Admin
exports.getUserStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();

    // Get new users in the last 7 days
    const lastWeekDate = new Date();
    lastWeekDate.setDate(lastWeekDate.getDate() - 7);

    const newUsers = await User.countDocuments({
      registrationDate: { $gte: lastWeekDate },
    });

    // Get active users in the last 7 days
    const activeUsers = await User.countDocuments({
      lastActivity: { $gte: lastWeekDate },
    });

    // Get total submissions
    const totalSubmissions = await Submission.countDocuments();

    res.status(200).json({
      success: true,
      stats: {
        totalUsers,
        newUsers,
        activeUsers,
        totalSubmissions,
      },
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
