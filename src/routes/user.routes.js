const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const {
  getUsers,
  getUser,
  updateUser,
  deleteUser,
  getUserStats,
  blockUser,
  unblockUser,
} = require('../controllers/user.controller');
const { protect } = require('../middleware/auth.middleware');

// All routes are protected
router.use(protect);

router.get('/stats', getUserStats);

router.route('/')
  .get(getUsers);

router.route('/:id')
  .get(getUser)
  .put(updateUser)
  .delete(deleteUser);

router.put('/:id/block', [
  check('reason').optional().isString().trim()
], blockUser);

router.put('/:id/unblock', unblockUser);

module.exports = router;
