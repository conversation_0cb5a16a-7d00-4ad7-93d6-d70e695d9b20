<!-- <PERSON><PERSON><PERSON><PERSON>turma Modalı -->
<div class="modal fade" id="createNotificationModal" tabindex="-1" aria-labelledby="createNotificationModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="createNotificationModalLabel" data-i18n="app.notifications.createTitle"><PERSON>ni
          Bildirim Oluştur</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-danger d-none" id="notificationError"></div>
        <form id="createNotificationForm" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="notificationTitle" class="form-label" data-i18n="app.notifications.titleLabel">Başlık</label>
            <input type="text" class="form-control" id="notificationTitle" required>
            <small class="form-text text-muted" data-i18n="app.notifications.titleHelp">Bildirim kampanya başlığı
              (Bildirim içerisinde yer almaz)</small>
          </div>

          <div class="mb-3">
            <label for="notificationMessage" class="form-label" data-i18n="app.notifications.messageLabel">Mesaj</label>
            <textarea class="form-control" id="notificationMessage" rows="5" required></textarea>
            <small class="form-text text-muted" data-i18n="app.notifications.messageHelp">
              Telegram Markdown formatını kullanabilirsiniz.
              <br>
              <strong>Örnek:</strong> *Kalın*, _İtalik_, [Link](https://example.com), `Kod`
            </small>
          </div>

          <div class="mb-3">
            <label for="notificationImage" class="form-label" data-i18n="app.notifications.imageLabel">Fotoğraf (İsteğe
              Bağlı)</label>
            <input type="file" class="form-control" id="notificationImage" accept="image/jpeg,image/png,image/gif">
            <small class="form-text text-muted" data-i18n="app.notifications.imageHelp">
              Maksimum dosya boyutu: 5MB. İzin verilen formatlar: JPG, PNG, GIF
            </small>
            <div id="imagePreviewContainer" class="mt-2 d-none">
              <img id="imagePreview" class="img-fluid img-thumbnail" style="max-height: 200px;">
              <button type="button" class="btn btn-sm btn-danger mt-1" id="removeImageBtn"
                data-i18n="app.notifications.removeImage">Fotoğrafı Kaldır</button>
            </div>
          </div>



          <div class="mb-3">
            <label data-i18n="app.notifications.buttons">Butonlar</label>
            <div id="notificationButtons" class="mb-2">
              <!-- Butonlar buraya eklenecek -->
            </div>
            <button type="button" class="btn btn-sm btn-secondary" id="addButtonBtn">
              <i class="fas fa-plus"></i> <span data-i18n="app.notifications.addButton">Buton
                Ekle</span>
            </button>
            <small class="form-text text-muted" data-i18n="app.notifications.buttonsHelp">
              Bildirime eklemek istediğiniz butonları ekleyin (maksimum 10 buton). Her buton için bir metin ve URL
              belirtmelisiniz.
            </small>
          </div>

          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="previewBeforeSend" checked>
              <label class="form-check-label" for="previewBeforeSend" data-i18n="app.notifications.previewBeforeSend">
                Göndermeden önce önizle
              </label>
            </div>
          </div>

          <div id="notificationError" class="alert alert-danger d-none"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
          data-i18n="app.common.cancel">İptal</button>
        <button type="button" class="btn btn-primary" id="createNotificationBtn"
          data-i18n="app.notifications.create">Oluştur</button>
      </div>
    </div>
  </div>
</div>

<!-- Bildirim Önizleme Modalı -->
<div class="modal fade" id="previewNotificationModal" tabindex="-1" aria-labelledby="previewNotificationModalLabel"
  aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="previewNotificationModalLabel" data-i18n="app.notifications.previewTitle">Bildirim
          Önizleme</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="card mb-3">
          <div class="card-header bg-light">
            <strong data-i18n="app.notifications.previewMessage">Mesaj Önizleme</strong>
          </div>
          <div class="card-body">
            <div id="previewMessageContent"></div>
          </div>
        </div>

        <div id="previewImageContainer" class="d-none mb-3">
          <div class="card">
            <div class="card-header bg-light">
              <strong data-i18n="app.notifications.previewImage">Fotoğraf Önizleme</strong>
            </div>
            <div class="card-body text-center">
              <img id="previewImage" class="img-fluid img-thumbnail" style="max-height: 300px;">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
          data-i18n="app.common.back">Geri</button>
        <button type="button" class="btn btn-primary" id="confirmSendBtn"
          data-i18n="app.notifications.send">Gönder</button>
      </div>
    </div>
  </div>
</div>

<!-- Bildirim Detay Modalı -->
<div class="modal fade" id="notificationDetailModal" tabindex="-1" aria-labelledby="notificationDetailModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="notificationDetailModalLabel" data-i18n="app.notifications.detailTitle">Bildirim
          Detayları</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="notificationDetailContent">
        <!-- Bildirim detayları buraya yüklenecek -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
          data-i18n="app.common.close">Kapat</button>
      </div>
    </div>
  </div>
</div>

<!-- Bildirim Rapor Modalı -->
<div class="modal fade" id="notificationReportModal" tabindex="-1" aria-labelledby="notificationReportModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="notificationReportModalLabel" data-i18n="app.notifications.reportTitle">Bildirim
          Raporu</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="notificationReportContent">
        <!-- Bildirim raporu buraya yüklenecek -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
          data-i18n="app.common.close">Kapat</button>
      </div>
    </div>
  </div>
</div>