// Load admins page
async function loadAdmins() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'admins';
  updateNavigation();

  // Make sure translations are loaded
  if (!translationsLoaded) {
    const currentLang = localStorage.getItem('i18nextLng') || 'tr';
    await loadTranslations(currentLang);
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get admins
    const response = await axios.get(`${API_BASE_URL}/admins`);
    const { admins } = response.data;

    // Render admins page
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.admins.title">Yöneticiler</h1>
        <button class="btn btn-success" onclick="loadCreateAdmin()" data-i18n="app.admins.create"><PERSON><PERSON></button>
      </div>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th data-i18n="app.admins.username">Kullanıcı Adı</th>
              <th data-i18n="app.admins.name">İsim</th>
              <th data-i18n="app.admins.email">E-posta</th>
              <th data-i18n="app.admins.role">Rol</th>
              <th data-i18n="app.admins.statusLabel">Durum</th>
              <th data-i18n="app.admins.lastLogin">Son Giriş</th>
              <th data-i18n="app.admins.actions">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            ${admins.length > 0 ? admins.map(admin => `
              <tr>
                <td>${admin.username}</td>
                <td>${admin.name}</td>
                <td>${admin.email}</td>
                <td>${admin.role === 'superadmin' ? i18next.t('app.admins.roles.superadmin') : i18next.t('app.admins.roles.admin')}</td>
                <td>
                  <span class="badge ${admin.isActive ? 'bg-success' : 'bg-danger'}">
                    ${admin.isActive ? i18next.t('app.admins.status.active') : i18next.t('app.admins.status.inactive')}
                  </span>
                </td>
                <td>${admin.lastLogin ? new Date(admin.lastLogin).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' }) : '-'}</td>
                <td>
                  <div class="btn-group">
                    <a href="#" onclick="viewAdmin('${admin._id}')" class="btn btn-sm btn-primary" data-i18n="app.admins.view">Görüntüle</a>
                    <a href="#" onclick="editAdmin('${admin._id}')" class="btn btn-sm btn-warning" data-i18n="app.admins.edit">Düzenle</a>
                    <a href="#" onclick="changeAdminPassword('${admin._id}')" class="btn btn-sm btn-info" data-i18n="app.admins.changePassword">Şifre Değiştir</a>
                    ${admin.role !== 'superadmin' ? `
                      <a href="#" onclick="deleteAdmin('${admin._id}')" class="btn btn-sm btn-danger" data-i18n="app.admins.delete">Sil</a>
                    ` : ''}
                  </div>
                </td>
              </tr>
            `).join('') : `
              <tr>
                <td colspan="7" class="text-center" data-i18n="app.admins.noAdmins">Yönetici bulunamadı</td>
              </tr>
            `}
          </tbody>
        </table>
      </div>
    `;
  } catch (error) {
    console.error('Admins error:', error);

    // Check if unauthorized
    if (error.response?.status === 403) {
      app.innerHTML = `
        <div class="alert alert-danger">
          <span data-i18n="app.common.unauthorized">Bu sayfaya erişim yetkiniz bulunmamaktadır.</span>
        </div>
      `;
      return;
    }

    app.innerHTML = `
      <div class="alert alert-danger">
        <span data-i18n="app.admins.error">Yöneticiler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</span>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// View admin details
async function viewAdmin(adminId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Make sure translations are loaded
  if (!translationsLoaded) {
    const currentLang = localStorage.getItem('i18nextLng') || 'tr';
    await loadTranslations(currentLang);
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get admin details
    const response = await axios.get(`${API_BASE_URL}/admins/${adminId}`);
    const { admin } = response.data;

    // Render admin details
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.adminDetail.title">Yönetici Detayları</h1>
        <button class="btn btn-secondary" onclick="loadAdmins()" data-i18n="app.adminDetail.back">Yönetici Listesine Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.adminDetail.info">Yönetici Bilgileri</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong data-i18n="app.adminDetail.username">Kullanıcı Adı:</strong> ${admin.username}</p>
              <p><strong data-i18n="app.adminDetail.name">İsim:</strong> ${admin.name}</p>
              <p><strong data-i18n="app.adminDetail.email">E-posta:</strong> ${admin.email}</p>
            </div>
            <div class="col-md-6">
              <p><strong data-i18n="app.adminDetail.role">Rol:</strong> ${admin.role === 'superadmin' ? i18next.t('app.admins.roles.superadmin') : i18next.t('app.admins.roles.admin')}</p>
              <p><strong data-i18n="app.adminDetail.status">Durum:</strong> ${admin.isActive ? i18next.t('app.admins.status.active') : i18next.t('app.admins.status.inactive')}</p>
              <p><strong data-i18n="app.adminDetail.lastLogin">Son Giriş:</strong> ${admin.lastLogin ? new Date(admin.lastLogin).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' }) : '-'}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="d-flex gap-2">
        <button class="btn btn-warning" onclick="editAdmin('${admin._id}')" data-i18n="app.admins.edit">Düzenle</button>
        <button class="btn btn-info" onclick="changeAdminPassword('${admin._id}')" data-i18n="app.admins.changePassword">Şifre Değiştir</button>
        ${admin.role !== 'superadmin' ? `
          <button class="btn btn-danger" onclick="deleteAdmin('${admin._id}')" data-i18n="app.admins.delete">Sil</button>
        ` : ''}
      </div>
    `;
  } catch (error) {
    console.error('Admin details error:', error);

    // Check if unauthorized
    if (error.response?.status === 403) {
      app.innerHTML = `
        <div class="alert alert-danger">
          <span data-i18n="app.common.unauthorized">Bu sayfaya erişim yetkiniz bulunmamaktadır.</span>
        </div>
        <div class="text-center mt-3">
          <button class="btn btn-primary" onclick="loadDashboard()" data-i18n="app.common.backToHome">Ana Sayfaya Dön</button>
        </div>
      `;
      return;
    }

    app.innerHTML = `
      <div class="alert alert-danger">
        <span data-i18n="app.adminDetail.error">Yönetici detayları yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</span>
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadAdmins()" data-i18n="app.adminDetail.back">Yönetici Listesine Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Create new admin
function loadCreateAdmin() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'create-admin';
  updateNavigation();

  // Make sure translations are loaded
  if (!translationsLoaded) {
    const currentLang = localStorage.getItem('i18nextLng') || 'tr';
    loadTranslations(currentLang);
  }

  app.innerHTML = `
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 data-i18n="app.adminForm.createTitle">Yeni Yönetici Ekle</h1>
      <button class="btn btn-secondary" onclick="loadAdmins()" data-i18n="app.adminDetail.back">Yönetici Listesine Dön</button>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0" data-i18n="app.adminDetail.info">Yönetici Bilgileri</h5>
      </div>
      <div class="card-body">
        <form id="create-admin-form">
          <div class="mb-3">
            <label for="username" class="form-label" data-i18n="app.adminForm.username">Kullanıcı Adı</label>
            <input type="text" class="form-control" id="username" required>
          </div>

          <div class="mb-3">
            <label for="password" class="form-label" data-i18n="app.adminForm.password">Şifre</label>
            <input type="password" class="form-control" id="password" required>
          </div>

          <div class="mb-3">
            <label for="email" class="form-label" data-i18n="app.adminForm.email">E-posta</label>
            <input type="email" class="form-control" id="email" required>
          </div>

          <div class="mb-3">
            <label for="name" class="form-label" data-i18n="app.adminForm.name">İsim</label>
            <input type="text" class="form-control" id="name" required>
          </div>

          <div class="mb-3">
            <label for="role" class="form-label" data-i18n="app.adminForm.role">Rol</label>
            <select class="form-select" id="role">
              <option value="admin" data-i18n="app.admins.roles.admin">Yönetici</option>
              <option value="superadmin" data-i18n="app.admins.roles.superadmin">Süper Yönetici</option>
            </select>
          </div>

          <div id="create-admin-error" class="alert alert-danger d-none"></div>

          <div class="d-flex gap-2">
            <button type="submit" class="btn btn-primary" data-i18n="app.adminForm.save">Yönetici Ekle</button>
            <button type="button" class="btn btn-secondary" onclick="loadAdmins()" data-i18n="app.adminForm.cancel">İptal</button>
          </div>
        </form>
      </div>
    </div>
  `;

  // Add event listener to form
  document.getElementById('create-admin-form').addEventListener('submit', handleCreateAdmin);

  // Update translations
  updateUI();
}

// Handle create admin form submission
async function handleCreateAdmin(e) {
  e.preventDefault();

  const errorElement = document.getElementById('create-admin-error');
  errorElement.classList.add('d-none');

  try {
    // Get form data
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const email = document.getElementById('email').value;
    const name = document.getElementById('name').value;
    const role = document.getElementById('role').value;

    // Create admin data
    const adminData = {
      username,
      password,
      email,
      name,
      role,
    };

    // Send request
    await axios.post(`${API_BASE_URL}/admins`, adminData);

    // Redirect to admins page
    loadAdmins();
  } catch (error) {
    console.error('Create admin error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.adminForm.createError');
    errorElement.classList.remove('d-none');
  }
}

// Edit admin
async function editAdmin(adminId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Make sure translations are loaded
  if (!translationsLoaded) {
    const currentLang = localStorage.getItem('i18nextLng') || 'tr';
    await loadTranslations(currentLang);
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get admin details
    const response = await axios.get(`${API_BASE_URL}/admins/${adminId}`);
    const { admin } = response.data;

    // Render edit form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.adminForm.editTitle">Yönetici Düzenle</h1>
        <button class="btn btn-secondary" onclick="viewAdmin('${adminId}')" data-i18n="app.adminDetail.back">Yönetici Detaylarına Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.adminDetail.info">Yönetici Bilgileri</h5>
        </div>
        <div class="card-body">
          <form id="edit-admin-form">
            <div class="mb-3">
              <label for="username" class="form-label" data-i18n="app.adminForm.username">Kullanıcı Adı</label>
              <input type="text" class="form-control" id="username" value="${admin.username}" required>
            </div>

            <div class="mb-3">
              <label for="email" class="form-label" data-i18n="app.adminForm.email">E-posta</label>
              <input type="email" class="form-control" id="email" value="${admin.email}" required>
            </div>

            <div class="mb-3">
              <label for="name" class="form-label" data-i18n="app.adminForm.name">İsim</label>
              <input type="text" class="form-control" id="name" value="${admin.name}" required>
            </div>

            ${state.admin.role === 'superadmin' ? `
              <div class="mb-3">
                <label for="role" class="form-label" data-i18n="app.adminForm.role">Rol</label>
                <select class="form-select" id="role">
                  <option value="admin" ${admin.role === 'admin' ? 'selected' : ''} data-i18n="app.admins.roles.admin">Yönetici</option>
                  <option value="superadmin" ${admin.role === 'superadmin' ? 'selected' : ''} data-i18n="app.admins.roles.superadmin">Süper Yönetici</option>
                </select>
              </div>

              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="isActive" ${admin.isActive ? 'checked' : ''}>
                <label class="form-check-label" for="isActive" data-i18n="app.adminForm.isActive">Aktif</label>
              </div>
            ` : ''}

            <div id="edit-admin-error" class="alert alert-danger d-none"></div>

            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary" data-i18n="app.adminForm.saveChanges">Değişiklikleri Kaydet</button>
              <button type="button" class="btn btn-secondary" onclick="viewAdmin('${adminId}')" data-i18n="app.adminForm.cancel">İptal</button>
            </div>
          </form>
        </div>
      </div>
    `;

    // Add event listener to form
    document.getElementById('edit-admin-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleEditAdmin(e, adminId);
    });

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Edit admin error:', error);

    // Check if unauthorized
    if (error.response?.status === 403) {
      app.innerHTML = `
        <div class="alert alert-danger">
          <span data-i18n="app.common.unauthorized">Bu işlem için yetkiniz bulunmamaktadır.</span>
        </div>
        <div class="text-center mt-3">
          <button class="btn btn-primary" onclick="loadDashboard()" data-i18n="app.common.backToHome">Ana Sayfaya Dön</button>
        </div>
      `;
      return;
    }

    app.innerHTML = `
      <div class="alert alert-danger">
        <span data-i18n="app.adminForm.editError">Yönetici düzenleme formu yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</span>
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadAdmins()" data-i18n="app.adminDetail.back">Yönetici Listesine Dön</button>
      </div>
    `;
  }
}

// Handle edit admin form submission
async function handleEditAdmin(e, adminId) {
  e.preventDefault();

  const errorElement = document.getElementById('edit-admin-error');
  errorElement.classList.add('d-none');

  try {
    // Get form data
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const name = document.getElementById('name').value;

    // Create admin data
    const adminData = {
      username,
      email,
      name,
    };

    // Add role and isActive if superadmin
    if (state.admin.role === 'superadmin') {
      const roleElement = document.getElementById('role');
      const isActiveElement = document.getElementById('isActive');

      if (roleElement) {
        adminData.role = roleElement.value;
      }

      if (isActiveElement) {
        adminData.isActive = isActiveElement.checked;
      }
    }

    // Send request
    await axios.put(`${API_BASE_URL}/admins/${adminId}`, adminData);

    // Redirect to admin details
    viewAdmin(adminId);
  } catch (error) {
    console.error('Edit admin error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.adminForm.editError');
    errorElement.classList.remove('d-none');
  }
}

// Change admin password
async function changeAdminPassword(adminId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Make sure translations are loaded
  if (!translationsLoaded) {
    const currentLang = localStorage.getItem('i18nextLng') || 'tr';
    await loadTranslations(currentLang);
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get admin details
    const response = await axios.get(`${API_BASE_URL}/admins/${adminId}`);
    const { admin } = response.data;

    // Render password change form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.changePassword.title">Şifre Değiştir</h1>
        <button class="btn btn-secondary" onclick="viewAdmin('${adminId}')" data-i18n="app.adminDetail.back">Yönetici Detaylarına Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">${admin.username} <span data-i18n="app.changePassword.for">için Şifre Değiştir</span></h5>
        </div>
        <div class="card-body">
          <form id="change-password-form">
            ${state.admin.role !== 'superadmin' || state.admin.id === adminId ? `
              <div class="mb-3">
                <label for="currentPassword" class="form-label" data-i18n="app.changePassword.currentPassword">Mevcut Şifre</label>
                <input type="password" class="form-control" id="currentPassword" required>
              </div>
            ` : ''}

            <div class="mb-3">
              <label for="newPassword" class="form-label" data-i18n="app.changePassword.newPassword">Yeni Şifre</label>
              <input type="password" class="form-control" id="newPassword" required>
            </div>

            <div class="mb-3">
              <label for="confirmPassword" class="form-label" data-i18n="app.changePassword.confirmPassword">Yeni Şifre (Tekrar)</label>
              <input type="password" class="form-control" id="confirmPassword" required>
            </div>

            <div id="change-password-error" class="alert alert-danger d-none"></div>

            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary" data-i18n="app.changePassword.save">Şifreyi Değiştir</button>
              <button type="button" class="btn btn-secondary" onclick="viewAdmin('${adminId}')" data-i18n="app.changePassword.cancel">İptal</button>
            </div>
          </form>
        </div>
      </div>
    `;

    // Add event listener to form
    document.getElementById('change-password-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleChangePassword(e, adminId);
    });

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Change password error:', error);

    // Check if unauthorized
    if (error.response?.status === 403) {
      app.innerHTML = `
        <div class="alert alert-danger">
          <span data-i18n="app.common.unauthorized">Bu işlem için yetkiniz bulunmamaktadır.</span>
        </div>
        <div class="text-center mt-3">
          <button class="btn btn-primary" onclick="loadDashboard()" data-i18n="app.common.backToHome">Ana Sayfaya Dön</button>
        </div>
      `;
      return;
    }

    app.innerHTML = `
      <div class="alert alert-danger">
        <span data-i18n="app.changePassword.loadError">Şifre değiştirme formu yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</span>
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadAdmins()" data-i18n="app.adminDetail.back">Yönetici Listesine Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Handle change password form submission
async function handleChangePassword(e, adminId) {
  e.preventDefault();

  const errorElement = document.getElementById('change-password-error');
  errorElement.classList.add('d-none');

  try {
    // Get form data
    const currentPasswordElement = document.getElementById('currentPassword');
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Check if passwords match
    if (newPassword !== confirmPassword) {
      throw new Error(i18next.t('app.changePassword.passwordMismatch'));
    }

    // Create password data
    const passwordData = {
      newPassword,
    };

    // Add current password if required
    if (currentPasswordElement) {
      passwordData.currentPassword = currentPasswordElement.value;
    }

    // Send request
    await axios.put(`${API_BASE_URL}/admins/${adminId}/change-password`, passwordData);

    // Redirect to admin details
    viewAdmin(adminId);
  } catch (error) {
    console.error('Change password error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.adminForm.passwordError');
    errorElement.classList.remove('d-none');
  }
}

// Delete admin
async function deleteAdmin(adminId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  if (!confirm(i18next.t('app.admins.confirmDelete'))) {
    return;
  }

  try {
    // Send request
    await axios.delete(`${API_BASE_URL}/admins/${adminId}`);

    // Redirect to admins page
    loadAdmins();
  } catch (error) {
    console.error('Delete admin error:', error);

    // Check if unauthorized
    if (error.response?.status === 403) {
      alert(i18next.t('app.common.unauthorized'));
      return;
    }

    alert(error.response?.data?.message || error.message || i18next.t('app.admins.deleteError'));
  }
}
