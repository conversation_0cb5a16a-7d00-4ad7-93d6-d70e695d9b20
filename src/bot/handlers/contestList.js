const Contest = require('../../models/Contest');
const { createContestListKeyboard } = require('../utils');
const { getTranslation } = require('../translations');

// Handle contest list
const handleContestList = async (bot, msg, user) => {
  const chatId = msg.chat.id;

  try {
    // Update last activity
    user.lastActivity = Date.now();
    await user.save();

    // Get active contests
    const now = new Date();
    const contests = await Contest.find({
      status: 'ACTIVE',
      startDate: { $lte: now },
      endDate: { $gte: now },
    }).sort({ endDate: 1 });

    if (contests.length === 0) {
      // Get no contests message
      const noContestsMessage = await getTranslation(user.language, 'bot.contests.noActive');

      return bot.sendMessage(
        chatId,
        noContestsMessage
      );
    }

    // Create contest list message
    const titleMessage = await getTranslation(user.language, 'bot.contests.title');
    const selectMessage = await getTranslation(user.language, 'bot.contests.select');

    let message = `🏆 *${titleMessage}* 🏆\n\n📋 ${selectMessage}\n\n`;

    // Format date and time helper function
    const formatDateTime = (date, locale) => {
      const dateObj = new Date(date);
      const dateStr = dateObj.toLocaleDateString(locale);

      // Format time as HH:MM
      const hours = dateObj.getHours().toString().padStart(2, '0');
      const minutes = dateObj.getMinutes().toString().padStart(2, '0');
      const timeStr = `${hours}:${minutes}`;

      return `${dateStr} ${timeStr}`;
    };

    // Process each contest
    for (let index = 0; index < contests.length; index++) {
      const contest = contests[index];
      message += `${index + 1}. 💰 *${contest.title}*\n`;

      // Get locale based on user language
      const locale = user.language === 'en' ? 'en-US' : user.language === 'de' ? 'de-DE' : user.language === 'ar' ? 'ar-SA' : 'tr-TR';

      // Get translations for labels
      const endsLabel = await getTranslation(user.language, 'bot.contests.ends');

      message += `   ⏰ ${endsLabel}: ${formatDateTime(contest.endDate, locale)}\n`;

      // Show different info based on contest type
      if (contest.type === 'EMOJI_GAME') {
        const gameTypeNames = {
          'DICE': '🎲 Zar',
          'BASKETBALL': '🏀 Basketbol',
          'FOOTBALL': '⚽ Futbol',
          'DART': '🎯 Dart',
          'BOWLING': '🎳 Bowling',
          'SLOT': '🎰 Slot'
        };
        const gameTypeName = gameTypeNames[contest.emojiGame?.gameType] || '🎮 Oyun';
        const attemptsLabel = await getTranslation(user.language, 'bot.contests.attempts') || 'Deneme';
        message += `   🎮 ${gameTypeName} (${contest.emojiGame?.attemptCount || 0} ${attemptsLabel})\n\n`;
      } else {
        const questionsLabel = await getTranslation(user.language, 'bot.contests.questions');
        message += `   ❓ ${questionsLabel}: ${contest.questions.length}\n\n`;
      }
    }

    // Create keyboard with contest options
    const keyboard = await createContestListKeyboard(contests, user.language);

    bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard,
    });
  } catch (error) {
    console.error('Error handling contest list:', error);
    // Get error message
    const errorMessage = await getTranslation(user.language, 'bot.errors.general');

    bot.sendMessage(
      chatId,
      errorMessage
    );
  }
};

module.exports = { handleContestList };
