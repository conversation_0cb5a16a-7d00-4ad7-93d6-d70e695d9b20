/**
 * JWT Secret kontrolü ve otomatik oluşturma
 * Bu betik, .env dosyasında JWT_SECRET değerinin olup olmadığını kontrol eder.
 * E<PERSON>er yoksa veya varsay<PERSON>lan de<PERSON>, gü<PERSON>li bir secret oluşturur ve .env dosyasını günceller.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const dotenv = require('dotenv');

// .env dosyasının yolunu belirle
const envPath = path.resolve(process.cwd(), '.env');

// JWT Secret kontrolü ve oluşturma
function ensureJwtSecret() {
  try {
    console.log('JWT Secret kontrolü yapılıyor...');
    
    // .env dosyasını oku
    let envContent = '';
    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (err) {
      console.error('.env dosyası okunamadı:', err.message);
      console.log('Yeni bir .env dosyası oluşturulacak.');
      envContent = '';
    }
    
    // .env içeriğini parse et
    const envConfig = dotenv.parse(envContent);
    
    // JWT_SECRET kontrolü
    const jwtSecret = envConfig.JWT_SECRET;
    const defaultSecrets = [
      'your_jwt_secret_here',
      'jwt_secret',
      'secret',
      'telegram_bot_loto_secret_key',
      'telegram_bot_loto_secret_key_2024',
      'your_secret_key',
      'change_this_secret'
    ];
    
    // Secret yoksa veya varsayılan değerlerden biriyse yeni bir secret oluştur
    if (!jwtSecret || defaultSecrets.includes(jwtSecret) || jwtSecret.length < 32) {
      console.log('Güvenli JWT Secret bulunamadı veya varsayılan değer kullanılıyor.');
      console.log('Yeni bir güvenli JWT Secret oluşturuluyor...');
      
      // Yeni güvenli secret oluştur
      const newSecret = crypto.randomBytes(64).toString('hex');
      
      // .env içeriğini güncelle
      let updatedEnvContent = '';
      
      if (envContent.includes('JWT_SECRET=')) {
        // Mevcut JWT_SECRET satırını güncelle
        updatedEnvContent = envContent.replace(
          /JWT_SECRET=.*/,
          `JWT_SECRET=${newSecret}`
        );
      } else {
        // JWT_SECRET satırı yoksa ekle
        updatedEnvContent = envContent + `\n# JWT Secret (Güvenli rastgele oluşturulmuş)\nJWT_SECRET=${newSecret}\n`;
        
        // JWT_EXPIRES_IN yoksa onu da ekle
        if (!envContent.includes('JWT_EXPIRES_IN=')) {
          updatedEnvContent += `JWT_EXPIRES_IN=24h\n`;
        }
      }
      
      // Güncellenmiş içeriği .env dosyasına yaz
      fs.writeFileSync(envPath, updatedEnvContent);
      
      console.log('JWT Secret başarıyla güncellendi.');
      
      // Güncellenmiş .env dosyasını yükle
      dotenv.config();
      
      return newSecret;
    } else {
      console.log('Mevcut JWT Secret güvenli görünüyor.');
      return jwtSecret;
    }
  } catch (error) {
    console.error('JWT Secret kontrolü sırasında hata oluştu:', error);
    return null;
  }
}

// Fonksiyonu dışa aktar
module.exports = ensureJwtSecret;

// Doğrudan çalıştırılırsa
if (require.main === module) {
  ensureJwtSecret();
}
